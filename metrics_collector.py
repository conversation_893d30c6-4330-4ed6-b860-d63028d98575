"""
Prometheus Metrics Collector for RabbitMQ
Collects metrics from RabbitMQ and exposes them in Prometheus format.
"""

from prometheus_client import Collector<PERSON><PERSON><PERSON><PERSON>, Gauge, Counter, Info, Histogram
from prometheus_client.core import GaugeMetricFamily, CounterMetricFamily, InfoMetricFamily
import structlog
import async<PERSON>
from typing import Dict, List, Any, Optional
from rabbitmq_client import RabbitMQClient, RabbitMQAPIError
import time

logger = structlog.get_logger()


class RabbitMQCollector:
    """Custom Prometheus collector for RabbitMQ metrics"""
    
    def __init__(self, rabbitmq_client: RabbitMQClient, enabled_metrics: Dict[str, bool], alert_manager=None):
        """
        Initialize the collector

        Args:
            rabbitmq_client: RabbitMQ API client instance
            enabled_metrics: Dictionary of metric types and their enabled status
            alert_manager: Alert manager for sending alerts
        """
        self.client = rabbitmq_client
        self.enabled_metrics = enabled_metrics
        self.alert_manager = alert_manager
        logger.info("RabbitMQ collector initialized", enabled_metrics=enabled_metrics)
    
    def collect(self):
        """Collect metrics from RabbitMQ and yield Prometheus metrics"""
        start_time = time.time()
        
        try:
            # Collect overview metrics
            if self.enabled_metrics.get('overview', True):
                yield from self._collect_overview_metrics()
            
            # Collect node metrics
            if self.enabled_metrics.get('nodes', True):
                yield from self._collect_node_metrics()
            
            # Collect queue metrics
            if self.enabled_metrics.get('queues', True):
                yield from self._collect_queue_metrics()
            
            # Collect exchange metrics
            if self.enabled_metrics.get('exchanges', True):
                yield from self._collect_exchange_metrics()
            
            # Collect connection metrics
            if self.enabled_metrics.get('connections', True):
                yield from self._collect_connection_metrics()
            
            # Collect channel metrics
            if self.enabled_metrics.get('channels', True):
                yield from self._collect_channel_metrics()
            
            # Collect consumer metrics
            if self.enabled_metrics.get('consumers', True):
                yield from self._collect_consumer_metrics()
            
            # Collection duration metric
            collection_duration = time.time() - start_time
            duration_metric = GaugeMetricFamily(
                'rabbitmq_exporter_collection_duration_seconds',
                'Time spent collecting metrics from RabbitMQ',
                value=collection_duration
            )
            yield duration_metric
            
            logger.debug("Metrics collection completed", duration=collection_duration)
            
        except RabbitMQAPIError as e:
            logger.error("Failed to collect metrics", error=str(e))

            # Send alert for metrics collection error
            if self.alert_manager:
                try:
                    asyncio.create_task(
                        self.alert_manager.handle_metrics_collection_error(e)
                    )
                except Exception as alert_error:
                    logger.error("Failed to send metrics collection alert", error=str(alert_error))

            # Yield an error metric
            error_metric = GaugeMetricFamily(
                'rabbitmq_exporter_collection_errors_total',
                'Number of errors during metric collection',
                value=1
            )
            yield error_metric
    
    def _collect_overview_metrics(self):
        """Collect cluster overview metrics"""
        try:
            overview = self.client.get_overview()
            
            # RabbitMQ version info
            version_info = InfoMetricFamily(
                'rabbitmq_version_info',
                'RabbitMQ version information',
                value={
                    'version': overview.get('rabbitmq_version', ''),
                    'erlang_version': overview.get('erlang_version', ''),
                    'cluster_name': overview.get('cluster_name', '')
                }
            )
            yield version_info
            
            # Message statistics
            message_stats = overview.get('message_stats', {})
            
            publish_total = CounterMetricFamily(
                'rabbitmq_messages_published_total',
                'Total number of messages published',
                value=message_stats.get('publish', 0)
            )
            yield publish_total
            
            deliver_total = CounterMetricFamily(
                'rabbitmq_messages_delivered_total',
                'Total number of messages delivered',
                value=message_stats.get('deliver_get', 0)
            )
            yield deliver_total
            
            ack_total = CounterMetricFamily(
                'rabbitmq_messages_acknowledged_total',
                'Total number of messages acknowledged',
                value=message_stats.get('ack', 0)
            )
            yield ack_total
            
            # Queue totals
            queue_totals = overview.get('queue_totals', {})
            
            messages_ready = GaugeMetricFamily(
                'rabbitmq_messages_ready',
                'Number of messages ready to be delivered',
                value=queue_totals.get('messages_ready', 0)
            )
            yield messages_ready
            
            messages_unacknowledged = GaugeMetricFamily(
                'rabbitmq_messages_unacknowledged',
                'Number of unacknowledged messages',
                value=queue_totals.get('messages_unacknowledged', 0)
            )
            yield messages_unacknowledged
            
            messages_total = GaugeMetricFamily(
                'rabbitmq_messages_total',
                'Total number of messages',
                value=queue_totals.get('messages', 0)
            )
            yield messages_total
            
            # Object totals
            object_totals = overview.get('object_totals', {})
            
            connections_total = GaugeMetricFamily(
                'rabbitmq_connections_total',
                'Total number of connections',
                value=object_totals.get('connections', 0)
            )
            yield connections_total
            
            channels_total = GaugeMetricFamily(
                'rabbitmq_channels_total',
                'Total number of channels',
                value=object_totals.get('channels', 0)
            )
            yield channels_total
            
            queues_total = GaugeMetricFamily(
                'rabbitmq_queues_total',
                'Total number of queues',
                value=object_totals.get('queues', 0)
            )
            yield queues_total
            
            consumers_total = GaugeMetricFamily(
                'rabbitmq_consumers_total',
                'Total number of consumers',
                value=object_totals.get('consumers', 0)
            )
            yield consumers_total
            
        except Exception as e:
            logger.error("Failed to collect overview metrics", error=str(e))
    
    def _collect_node_metrics(self):
        """Collect node-specific metrics"""
        try:
            nodes = self.client.get_nodes()
            
            for node in nodes:
                node_name = node.get('name', 'unknown')
                labels = ['node']
                label_values = [node_name]
                
                # Node running status
                running = GaugeMetricFamily(
                    'rabbitmq_node_running',
                    'Whether the node is running',
                    labels=labels
                )
                running.add_metric(label_values, 1 if node.get('running', False) else 0)
                yield running
                
                # Memory usage
                mem_used = GaugeMetricFamily(
                    'rabbitmq_node_memory_used_bytes',
                    'Memory used by the node in bytes',
                    labels=labels
                )
                mem_used.add_metric(label_values, node.get('mem_used', 0))
                yield mem_used
                
                # Disk free space
                disk_free = GaugeMetricFamily(
                    'rabbitmq_node_disk_free_bytes',
                    'Free disk space in bytes',
                    labels=labels
                )
                disk_free.add_metric(label_values, node.get('disk_free', 0))
                yield disk_free
                
                # File descriptors
                fd_used = GaugeMetricFamily(
                    'rabbitmq_node_file_descriptors_used',
                    'Number of file descriptors used',
                    labels=labels
                )
                fd_used.add_metric(label_values, node.get('fd_used', 0))
                yield fd_used
                
                fd_total = GaugeMetricFamily(
                    'rabbitmq_node_file_descriptors_total',
                    'Total number of file descriptors available',
                    labels=labels
                )
                fd_total.add_metric(label_values, node.get('fd_total', 0))
                yield fd_total
                
                # Sockets
                sockets_used = GaugeMetricFamily(
                    'rabbitmq_node_sockets_used',
                    'Number of sockets used',
                    labels=labels
                )
                sockets_used.add_metric(label_values, node.get('sockets_used', 0))
                yield sockets_used
                
                sockets_total = GaugeMetricFamily(
                    'rabbitmq_node_sockets_total',
                    'Total number of sockets available',
                    labels=labels
                )
                sockets_total.add_metric(label_values, node.get('sockets_total', 0))
                yield sockets_total
                
        except Exception as e:
            logger.error("Failed to collect node metrics", error=str(e))

    def _collect_queue_metrics(self):
        """Collect queue-specific metrics"""
        try:
            queues = self.client.get_queues()

            for queue in queues:
                queue_name = queue.get('name', 'unknown')
                vhost = queue.get('vhost', '/')
                labels = ['queue', 'vhost']
                label_values = [queue_name, vhost]

                # Queue messages
                messages = GaugeMetricFamily(
                    'rabbitmq_queue_messages',
                    'Number of messages in queue',
                    labels=labels
                )
                messages.add_metric(label_values, queue.get('messages', 0))
                yield messages

                messages_ready = GaugeMetricFamily(
                    'rabbitmq_queue_messages_ready',
                    'Number of messages ready in queue',
                    labels=labels
                )
                messages_ready.add_metric(label_values, queue.get('messages_ready', 0))
                yield messages_ready

                messages_unacked = GaugeMetricFamily(
                    'rabbitmq_queue_messages_unacknowledged',
                    'Number of unacknowledged messages in queue',
                    labels=labels
                )
                messages_unacked.add_metric(label_values, queue.get('messages_unacknowledged', 0))
                yield messages_unacked

                # Queue consumers
                consumers = GaugeMetricFamily(
                    'rabbitmq_queue_consumers',
                    'Number of consumers for queue',
                    labels=labels
                )
                consumers.add_metric(label_values, queue.get('consumers', 0))
                yield consumers

                # Queue memory
                memory = GaugeMetricFamily(
                    'rabbitmq_queue_memory_bytes',
                    'Memory used by queue in bytes',
                    labels=labels
                )
                memory.add_metric(label_values, queue.get('memory', 0))
                yield memory

                # Message statistics
                message_stats = queue.get('message_stats', {})

                publish_total = CounterMetricFamily(
                    'rabbitmq_queue_messages_published_total',
                    'Total messages published to queue',
                    labels=labels
                )
                publish_total.add_metric(label_values, message_stats.get('publish', 0))
                yield publish_total

                deliver_total = CounterMetricFamily(
                    'rabbitmq_queue_messages_delivered_total',
                    'Total messages delivered from queue',
                    labels=labels
                )
                deliver_total.add_metric(label_values, message_stats.get('deliver_get', 0))
                yield deliver_total

                ack_total = CounterMetricFamily(
                    'rabbitmq_queue_messages_acknowledged_total',
                    'Total messages acknowledged from queue',
                    labels=labels
                )
                ack_total.add_metric(label_values, message_stats.get('ack', 0))
                yield ack_total

        except Exception as e:
            logger.error("Failed to collect queue metrics", error=str(e))

    def _collect_exchange_metrics(self):
        """Collect exchange-specific metrics"""
        try:
            exchanges = self.client.get_exchanges()

            for exchange in exchanges:
                exchange_name = exchange.get('name', 'unknown')
                vhost = exchange.get('vhost', '/')
                exchange_type = exchange.get('type', 'unknown')
                labels = ['exchange', 'vhost', 'type']
                label_values = [exchange_name, vhost, exchange_type]

                # Exchange message statistics
                message_stats = exchange.get('message_stats', {})

                publish_in_total = CounterMetricFamily(
                    'rabbitmq_exchange_messages_published_in_total',
                    'Total messages published to exchange',
                    labels=labels
                )
                publish_in_total.add_metric(label_values, message_stats.get('publish_in', 0))
                yield publish_in_total

                publish_out_total = CounterMetricFamily(
                    'rabbitmq_exchange_messages_published_out_total',
                    'Total messages published out from exchange',
                    labels=labels
                )
                publish_out_total.add_metric(label_values, message_stats.get('publish_out', 0))
                yield publish_out_total

        except Exception as e:
            logger.error("Failed to collect exchange metrics", error=str(e))

    def _collect_connection_metrics(self):
        """Collect connection-specific metrics"""
        try:
            connections = self.client.get_connections()

            for connection in connections:
                conn_name = connection.get('name', 'unknown')
                user = connection.get('user', 'unknown')
                vhost = connection.get('vhost', '/')
                labels = ['connection', 'user', 'vhost']
                label_values = [conn_name, user, vhost]

                # Connection state
                state = connection.get('state', 'unknown')
                conn_state = GaugeMetricFamily(
                    'rabbitmq_connection_state',
                    'Connection state (1 for running, 0 for others)',
                    labels=labels + ['state']
                )
                conn_state.add_metric(label_values + [state], 1 if state == 'running' else 0)
                yield conn_state

                # Connection channels
                channels = GaugeMetricFamily(
                    'rabbitmq_connection_channels',
                    'Number of channels for connection',
                    labels=labels
                )
                channels.add_metric(label_values, connection.get('channels', 0))
                yield channels

        except Exception as e:
            logger.error("Failed to collect connection metrics", error=str(e))

    def _collect_channel_metrics(self):
        """Collect channel-specific metrics"""
        try:
            channels = self.client.get_channels()

            channel_count = GaugeMetricFamily(
                'rabbitmq_channels_count',
                'Number of channels',
                value=len(channels)
            )
            yield channel_count

        except Exception as e:
            logger.error("Failed to collect channel metrics", error=str(e))

    def _collect_consumer_metrics(self):
        """Collect consumer-specific metrics"""
        try:
            consumers = self.client.get_consumers()

            consumer_count = GaugeMetricFamily(
                'rabbitmq_consumers_count',
                'Number of consumers',
                value=len(consumers)
            )
            yield consumer_count

        except Exception as e:
            logger.error("Failed to collect consumer metrics", error=str(e))

#!/bin/bash

# 测试修复是否有效

echo "=== Shell Compatibility Test ==="
echo "Current shell: $0"
echo "Bash version: ${BASH_VERSION:-Not available}"
echo ""

# 测试数字检查函数
is_number() {
    case $1 in
        ''|*[!0-9]*) return 1 ;;
        *) return 0 ;;
    esac
}

echo "=== Testing is_number function ==="
test_values="200 404 abc 123abc '' 500 0"
for val in $test_values; do
    if [ -z "$val" ]; then
        val="(empty)"
    fi
    
    if is_number "$val"; then
        echo "✅ '$val' is a number"
    else
        echo "❌ '$val' is not a number"
    fi
done

echo ""
echo "=== Testing HTTP status validation ==="
test_codes="200 201 300 400 404 500 abc"
for code in $test_codes; do
    if is_number "$code" && [ "$code" -ge 200 ] && [ "$code" -lt 400 ]; then
        echo "✅ HTTP $code is success"
    else
        echo "❌ HTTP $code is failure"
    fi
done

echo ""
echo "=== Testing curl command ==="
if command -v curl >/dev/null 2>&1; then
    echo "✅ curl command found"
    curl --version | head -1
else
    echo "❌ curl command not found"
fi

echo ""
echo "=== Testing date command ==="
if command -v date >/dev/null 2>&1; then
    echo "✅ date command found"
    echo "Current time: $(date +'%Y-%m-%d %H:%M:%S')"
else
    echo "❌ date command not found"
fi

echo ""
echo "=== Testing hostname command ==="
if command -v hostname >/dev/null 2>&1; then
    echo "✅ hostname command found"
    echo "Hostname: $(hostname)"
else
    echo "❌ hostname command not found"
fi

echo ""
echo "=== Testing JSON construction ==="
test_message="Test message with special chars: 测试 & < > \" '"
json_data="{\"msgtype\":\"text\",\"text\":{\"content\":\"$test_message\"}}"
echo "JSON: $json_data"

echo ""
echo "=== All tests completed ==="
echo "If no errors appeared above, the script should work in Ubuntu."

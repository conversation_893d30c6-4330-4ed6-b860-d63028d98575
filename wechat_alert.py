"""
企业微信告警模块
用于发送RabbitMQ监控告警到企业微信群
"""

import time
import json
import socket
import platform
from typing import Optional, Dict, Any
from datetime import datetime
import structlog
import httpx

logger = structlog.get_logger()


class WeChatAlert:
    """企业微信告警类"""
    
    def __init__(self, webhook_url: str, alert_threshold: int = 3, 
                 alert_interval: int = 300, enabled: bool = True):
        """
        初始化企业微信告警
        
        Args:
            webhook_url: 企业微信机器人webhook地址
            alert_threshold: 连续失败多少次后发送告警
            alert_interval: 告警间隔（秒），避免频繁发送
            enabled: 是否启用告警
        """
        self.webhook_url = webhook_url
        self.alert_threshold = alert_threshold
        self.alert_interval = alert_interval
        self.enabled = enabled
        self.last_alert_time = 0
        self.consecutive_failures = 0
        self.last_status = "unknown"
        self.hostname = socket.gethostname()
        self.system_info = f"{platform.system()} {platform.release()}"
        
        logger.info("WeChat alert initialized", 
                   enabled=enabled, 
                   threshold=alert_threshold,
                   interval=alert_interval,
                   hostname=self.hostname)
    
    def should_send_alert(self) -> bool:
        """检查是否应该发送告警"""
        if not self.enabled:
            return False
        
        current_time = time.time()
        return (current_time - self.last_alert_time) >= self.alert_interval
    
    def record_failure(self) -> bool:
        """
        记录失败事件
        
        Returns:
            是否应该发送告警
        """
        self.consecutive_failures += 1
        
        # 达到阈值或每10次失败发送一次告警
        if (self.consecutive_failures == self.alert_threshold or 
            (self.consecutive_failures > self.alert_threshold and 
             self.consecutive_failures % 10 == 0)):
            return self.should_send_alert()
        
        return False
    
    def record_success(self) -> bool:
        """
        记录成功事件
        
        Returns:
            是否应该发送恢复通知
        """
        # 如果之前有连续失败，现在恢复了，发送恢复通知
        should_notify = (self.consecutive_failures >= self.alert_threshold and 
                        self.should_send_alert())
        
        self.consecutive_failures = 0
        return should_notify
    
    async def send_alert(self, alert_type: str, title: str, details: Dict[str, Any]):
        """
        发送告警消息到企业微信
        
        Args:
            alert_type: 告警类型 (failure, recovery, warning)
            title: 告警标题
            details: 详细信息
        """
        if not self.enabled:
            logger.debug("WeChat alert disabled, skipping")
            return
        
        try:
            # 构造告警消息
            message = self._build_alert_message(alert_type, title, details)
            
            # 发送消息
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    self.webhook_url,
                    json=message,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info("WeChat alert sent successfully", 
                                   alert_type=alert_type, title=title)
                        self.last_alert_time = time.time()
                    else:
                        logger.error("WeChat API error", 
                                   errcode=result.get("errcode"),
                                   errmsg=result.get("errmsg"))
                else:
                    logger.error("Failed to send WeChat alert", 
                               status_code=response.status_code,
                               response=response.text)
                    
        except Exception as e:
            logger.error("Exception while sending WeChat alert", error=str(e))
    
    def _build_alert_message(self, alert_type: str, title: str, details: Dict[str, Any]) -> Dict[str, Any]:
        """构造告警消息"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 根据告警类型选择图标和颜色
        icons = {
            "failure": "🚨",
            "recovery": "✅", 
            "warning": "⚠️",
            "info": "ℹ️"
        }
        
        colors = {
            "failure": "warning",
            "recovery": "info",
            "warning": "warning", 
            "info": "comment"
        }
        
        icon = icons.get(alert_type, "📢")
        color = colors.get(alert_type, "comment")
        
        # 构造Markdown消息
        if alert_type == "failure":
            content = f"""{icon} **RabbitMQ监控告警** {icon}

**告警类型**: 连接异常
**主机名**: {self.hostname}
**系统信息**: {self.system_info}
**RabbitMQ地址**: {details.get('rabbitmq_url', 'Unknown')}
**连续失败次数**: {self.consecutive_failures}
**错误信息**: {details.get('error_message', 'Unknown error')}
**时间**: {current_time}

**建议操作**:
1. 检查RabbitMQ服务状态
2. 验证网络连接
3. 检查认证信息
4. 查看RabbitMQ日志"""

        elif alert_type == "recovery":
            content = f"""{icon} **RabbitMQ监控恢复** {icon}

**主机名**: {self.hostname}
**RabbitMQ地址**: {details.get('rabbitmq_url', 'Unknown')}
**状态**: 服务已恢复正常
**恢复时间**: {current_time}
**之前失败次数**: {details.get('previous_failures', 0)}"""

        elif alert_type == "warning":
            content = f"""{icon} **RabbitMQ监控警告** {icon}

**主机名**: {self.hostname}
**警告类型**: {title}
**详细信息**: {details.get('message', 'No details')}
**时间**: {current_time}"""

        else:  # info
            content = f"""{icon} **RabbitMQ监控信息** {icon}

**主机名**: {self.hostname}
**信息**: {title}
**详细信息**: {details.get('message', 'No details')}
**时间**: {current_time}"""
        
        return {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
    
    def send_startup_notification(self, rabbitmq_url: str, port: int):
        """发送启动通知"""
        if not self.enabled:
            return
        
        import asyncio
        
        details = {
            "rabbitmq_url": rabbitmq_url,
            "exporter_port": port,
            "message": f"RabbitMQ Exporter已启动，监听端口: {port}"
        }
        
        try:
            asyncio.create_task(
                self.send_alert("info", "RabbitMQ Exporter启动", details)
            )
        except Exception as e:
            logger.error("Failed to send startup notification", error=str(e))
    
    def send_shutdown_notification(self):
        """发送关闭通知"""
        if not self.enabled:
            return
        
        import asyncio
        
        details = {
            "message": "RabbitMQ Exporter已停止运行"
        }
        
        try:
            asyncio.create_task(
                self.send_alert("info", "RabbitMQ Exporter停止", details)
            )
        except Exception as e:
            logger.error("Failed to send shutdown notification", error=str(e))


class AlertManager:
    """告警管理器，统一管理各种告警"""
    
    def __init__(self, wechat_alert: Optional[WeChatAlert] = None):
        """
        初始化告警管理器
        
        Args:
            wechat_alert: 企业微信告警实例
        """
        self.wechat_alert = wechat_alert
        self.logger = structlog.get_logger()
    
    async def handle_rabbitmq_connection_error(self, error: Exception, rabbitmq_url: str):
        """处理RabbitMQ连接错误"""
        if self.wechat_alert and self.wechat_alert.record_failure():
            details = {
                "rabbitmq_url": rabbitmq_url,
                "error_message": str(error)
            }
            await self.wechat_alert.send_alert(
                "failure", 
                "RabbitMQ连接失败", 
                details
            )
    
    async def handle_rabbitmq_connection_recovery(self, rabbitmq_url: str):
        """处理RabbitMQ连接恢复"""
        if self.wechat_alert and self.wechat_alert.record_success():
            details = {
                "rabbitmq_url": rabbitmq_url,
                "previous_failures": self.wechat_alert.consecutive_failures
            }
            await self.wechat_alert.send_alert(
                "recovery",
                "RabbitMQ连接恢复", 
                details
            )
    
    async def handle_metrics_collection_error(self, error: Exception):
        """处理指标收集错误"""
        if self.wechat_alert and self.wechat_alert.should_send_alert():
            details = {
                "error_message": str(error),
                "message": "指标收集过程中发生错误"
            }
            await self.wechat_alert.send_alert(
                "warning",
                "指标收集异常",
                details
            )

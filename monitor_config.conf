# 多域名监控配置文件
# 格式: 名称|URL|方法|请求体|请求头
# 注意: 如果字段为空，请保留分隔符 |

# 主要服务
EasyView主站|http://sp.easyview.com.hk|POST|{"status": "ok"}|Content-Type: application/json
EasyView API|http://api.easyview.com.hk/health|GET||

# 外部服务依赖
百度首页|https://www.baidu.com|GET||
谷歌首页|https://www.google.com|GET||
GitHub|https://github.com|GET||

# 内部服务
本地API|http://localhost:8080/health|GET||
数据库服务|http://localhost:3306/ping|GET||
Redis服务|http://localhost:6379/ping|GET||

# 第三方API
微信API|https://api.weixin.qq.com|GET||
支付宝API|https://openapi.alipay.com|GET||

# CDN和静态资源
CDN主节点|https://cdn.example.com/health|GET||
图片服务|https://img.example.com/health|GET||

# 监控和日志服务
Prometheus|http://localhost:9090/-/healthy|GET||
Grafana|http://localhost:3000/api/health|GET||
ELK Stack|http://localhost:9200/_cluster/health|GET||

# 示例：带认证的API
# 认证API|https://api.example.com/status|GET||Authorization: Bearer YOUR_TOKEN

# 示例：POST请求
# 用户服务|http://user-service.com/health|POST|{"check": "health"}|Content-Type: application/json

# 示例：自定义端口
# 自定义服务|http://*************:8888/status|GET||

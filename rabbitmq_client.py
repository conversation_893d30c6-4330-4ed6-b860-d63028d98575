"""
RabbitMQ Management API Client
Provides methods to interact with RabbitMQ Management API and fetch metrics data.
"""

import requests
import json
import structlog
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin
import time

logger = structlog.get_logger()


class RabbitMQAPIError(Exception):
    """Custom exception for RabbitMQ API errors"""
    pass


class RabbitMQClient:
    """Client for RabbitMQ Management API"""
    
    def __init__(self, url: str, username: str, password: str, 
                 timeout: int = 10, verify_ssl: bool = True):
        """
        Initialize RabbitMQ client
        
        Args:
            url: RabbitMQ Management API base URL
            username: Authentication username
            password: Authentication password
            timeout: Request timeout in seconds
            verify_ssl: Whether to verify SSL certificates
        """
        self.base_url = url.rstrip('/')
        self.auth = (username, password)
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.session = requests.Session()
        self.session.auth = self.auth
        self.session.verify = verify_ssl
        
        logger.info("RabbitMQ client initialized", 
                   url=self.base_url, username=username)
    
    def _make_request(self, endpoint: str) -> Dict[str, Any]:
        """
        Make HTTP request to RabbitMQ API
        
        Args:
            endpoint: API endpoint path
            
        Returns:
            JSON response data
            
        Raises:
            RabbitMQAPIError: If request fails
        """
        url = urljoin(self.base_url + '/', f'api/{endpoint}')
        
        try:
            logger.debug("Making API request", url=url)
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error("API request failed", url=url, error=str(e))
            raise RabbitMQAPIError(f"Failed to fetch {endpoint}: {e}")
        except json.JSONDecodeError as e:
            logger.error("Failed to decode JSON response", url=url, error=str(e))
            raise RabbitMQAPIError(f"Invalid JSON response from {endpoint}: {e}")
    
    def get_overview(self) -> Dict[str, Any]:
        """Get cluster overview information"""
        return self._make_request('overview')
    
    def get_nodes(self) -> List[Dict[str, Any]]:
        """Get information about cluster nodes"""
        return self._make_request('nodes')
    
    def get_queues(self, vhost: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get queue information
        
        Args:
            vhost: Virtual host name (optional, gets all queues if None)
        """
        if vhost:
            endpoint = f'queues/{requests.utils.quote(vhost, safe="")}'
        else:
            endpoint = 'queues'
        return self._make_request(endpoint)
    
    def get_exchanges(self, vhost: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get exchange information
        
        Args:
            vhost: Virtual host name (optional, gets all exchanges if None)
        """
        if vhost:
            endpoint = f'exchanges/{requests.utils.quote(vhost, safe="")}'
        else:
            endpoint = 'exchanges'
        return self._make_request(endpoint)
    
    def get_connections(self) -> List[Dict[str, Any]]:
        """Get connection information"""
        return self._make_request('connections')
    
    def get_channels(self) -> List[Dict[str, Any]]:
        """Get channel information"""
        return self._make_request('channels')
    
    def get_consumers(self, vhost: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get consumer information
        
        Args:
            vhost: Virtual host name (optional, gets all consumers if None)
        """
        if vhost:
            endpoint = f'consumers/{requests.utils.quote(vhost, safe="")}'
        else:
            endpoint = 'consumers'
        return self._make_request(endpoint)
    
    def get_vhosts(self) -> List[Dict[str, Any]]:
        """Get virtual host information"""
        return self._make_request('vhosts')
    
    def health_check(self) -> bool:
        """
        Check if RabbitMQ API is accessible
        
        Returns:
            True if API is accessible, False otherwise
        """
        try:
            self.get_overview()
            logger.info("RabbitMQ health check passed")
            return True
        except RabbitMQAPIError:
            logger.error("RabbitMQ health check failed")
            return False

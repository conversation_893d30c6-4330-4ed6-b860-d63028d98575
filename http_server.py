"""
HTTP Server for RabbitMQ Exporter
Provides HTTP endpoints for Prometheus metrics and health checks.
"""

from http.server import <PERSON>TTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse
import threading
import structlog
from prometheus_client import CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
from metrics_collector import <PERSON><PERSON>Q<PERSON>ollector
from rabbitmq_client import RabbitMQClient
import json
import time

logger = structlog.get_logger()


class MetricsHandler(BaseHTTPRequestHandler):
    """HTTP request handler for metrics endpoints"""
    
    def __init__(self, registry: CollectorRegistry, rabbitmq_client: RabbitMQClient, *args, **kwargs):
        self.registry = registry
        self.rabbitmq_client = rabbitmq_client
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/metrics':
            self._handle_metrics()
        elif parsed_path.path == '/health':
            self._handle_health()
        elif parsed_path.path == '/':
            self._handle_root()
        else:
            self._handle_not_found()
    
    def _handle_metrics(self):
        """Handle /metrics endpoint"""
        try:
            logger.debug("Serving metrics endpoint")
            start_time = time.time()
            
            # Generate Prometheus metrics
            metrics_data = generate_latest(self.registry)
            
            duration = time.time() - start_time
            logger.debug("Metrics generated", duration=duration, size=len(metrics_data))
            
            # Send response
            self.send_response(200)
            self.send_header('Content-Type', CONTENT_TYPE_LATEST)
            self.send_header('Content-Length', str(len(metrics_data)))
            self.end_headers()
            self.wfile.write(metrics_data)
            
        except Exception as e:
            logger.error("Failed to generate metrics", error=str(e))
            self._send_error_response(500, f"Internal server error: {e}")
    
    def _handle_health(self):
        """Handle /health endpoint"""
        try:
            logger.debug("Serving health endpoint")
            
            # Check RabbitMQ connectivity
            is_healthy = self.rabbitmq_client.health_check()
            
            status_code = 200 if is_healthy else 503
            health_data = {
                'status': 'healthy' if is_healthy else 'unhealthy',
                'timestamp': time.time(),
                'rabbitmq_accessible': is_healthy
            }
            
            response_data = json.dumps(health_data, indent=2).encode('utf-8')
            
            self.send_response(status_code)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Content-Length', str(len(response_data)))
            self.end_headers()
            self.wfile.write(response_data)
            
        except Exception as e:
            logger.error("Failed to check health", error=str(e))
            self._send_error_response(500, f"Health check failed: {e}")
    
    def _handle_root(self):
        """Handle root endpoint"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>RabbitMQ Exporter</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 600px; }
                .endpoint { margin: 20px 0; padding: 10px; background-color: #f5f5f5; border-radius: 5px; }
                a { color: #0066cc; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>RabbitMQ Exporter</h1>
                <p>Prometheus exporter for RabbitMQ metrics</p>
                
                <div class="endpoint">
                    <h3><a href="/metrics">/metrics</a></h3>
                    <p>Prometheus metrics endpoint</p>
                </div>
                
                <div class="endpoint">
                    <h3><a href="/health">/health</a></h3>
                    <p>Health check endpoint</p>
                </div>
            </div>
        </body>
        </html>
        """.encode('utf-8')
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.send_header('Content-Length', str(len(html_content)))
        self.end_headers()
        self.wfile.write(html_content)
    
    def _handle_not_found(self):
        """Handle 404 errors"""
        self._send_error_response(404, "Not Found")
    
    def _send_error_response(self, status_code: int, message: str):
        """Send error response"""
        error_data = {
            'error': message,
            'status_code': status_code,
            'timestamp': time.time()
        }
        response_data = json.dumps(error_data, indent=2).encode('utf-8')
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_data)))
        self.end_headers()
        self.wfile.write(response_data)
    
    def log_message(self, format, *args):
        """Override default logging to use structlog"""
        logger.info("HTTP request", 
                   method=self.command,
                   path=self.path,
                   client=self.client_address[0],
                   user_agent=self.headers.get('User-Agent', ''))


class MetricsServer:
    """HTTP server for exposing metrics"""
    
    def __init__(self, port: int, rabbitmq_client: RabbitMQClient, 
                 collector: RabbitMQCollector):
        """
        Initialize metrics server
        
        Args:
            port: Port to listen on
            rabbitmq_client: RabbitMQ client instance
            collector: Metrics collector instance
        """
        self.port = port
        self.rabbitmq_client = rabbitmq_client
        self.collector = collector
        self.registry = CollectorRegistry()
        self.registry.register(collector)
        self.server = None
        self.server_thread = None
        
        logger.info("Metrics server initialized", port=port)
    
    def start(self):
        """Start the HTTP server"""
        try:
            # Create handler class with injected dependencies
            def handler_factory(*args, **kwargs):
                return MetricsHandler(self.registry, self.rabbitmq_client, *args, **kwargs)
            
            # Create and start server
            self.server = HTTPServer(('', self.port), handler_factory)
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            logger.info("Metrics server started", port=self.port)
            
        except Exception as e:
            logger.error("Failed to start metrics server", port=self.port, error=str(e))
            raise
    
    def stop(self):
        """Stop the HTTP server"""
        if self.server:
            logger.info("Stopping metrics server")
            self.server.shutdown()
            self.server.server_close()
            if self.server_thread:
                self.server_thread.join()
            logger.info("Metrics server stopped")
    
    def is_running(self) -> bool:
        """Check if server is running"""
        return self.server is not None and self.server_thread is not None and self.server_thread.is_alive()

#!/usr/bin/env python3
"""
Test script for RabbitMQ Exporter
Basic tests to verify functionality without requiring pytest.
"""

import sys
import time
import json
import tempfile
import os
from unittest.mock import Mock, patch

# Import our modules
from config import Config<PERSON>oader, Config
from rabbitmq_client import RabbitMQClient, RabbitMQAPIError
from metrics_collector import RabbitMQCollector


def test_config_loading():
    """Test configuration loading"""
    print("Testing configuration loading...")
    
    # Test default configuration
    config_loader = ConfigLoader()
    config = config_loader.load()
    
    assert config.rabbitmq.url == "http://localhost:15672"
    assert config.rabbitmq.username == "guest"
    assert config.exporter.port == 9419
    print("✓ Default configuration loaded successfully")
    
    # Test YAML configuration
    yaml_content = """
rabbitmq:
  url: "http://test:15672"
  username: "testuser"
  password: "testpass"
exporter:
  port: 9999
  log_level: "DEBUG"
metrics:
  overview: false
  queues: true
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(yaml_content)
        temp_file = f.name
    
    try:
        config_loader = ConfigLoader(temp_file)
        config = config_loader.load()
        
        assert config.rabbitmq.url == "http://test:15672"
        assert config.rabbitmq.username == "testuser"
        assert config.exporter.port == 9999
        assert config.exporter.log_level == "DEBUG"
        assert config.metrics.overview == False
        assert config.metrics.queues == True
        print("✓ YAML configuration loaded successfully")
        
    finally:
        os.unlink(temp_file)
    
    # Test environment variables
    os.environ['RABBITMQ_EXPORTER_RABBITMQ_URL'] = 'http://env:15672'
    os.environ['RABBITMQ_EXPORTER_EXPORTER_PORT'] = '8888'
    
    config_loader = ConfigLoader()
    config = config_loader.load()
    
    assert config.rabbitmq.url == "http://env:15672"
    assert config.exporter.port == 8888
    print("✓ Environment variables loaded successfully")
    
    # Clean up
    del os.environ['RABBITMQ_EXPORTER_RABBITMQ_URL']
    del os.environ['RABBITMQ_EXPORTER_EXPORTER_PORT']


def test_rabbitmq_client():
    """Test RabbitMQ client with mocked responses"""
    print("\nTesting RabbitMQ client...")
    
    # Mock successful response
    mock_response = Mock()
    mock_response.json.return_value = {
        'rabbitmq_version': '3.8.0',
        'cluster_name': 'test-cluster',
        'message_stats': {'publish': 100, 'deliver_get': 90},
        'queue_totals': {'messages': 10, 'messages_ready': 5},
        'object_totals': {'connections': 2, 'queues': 3}
    }
    mock_response.raise_for_status.return_value = None
    
    with patch('requests.Session.get', return_value=mock_response):
        client = RabbitMQClient(
            url="http://localhost:15672",
            username="guest",
            password="guest"
        )
        
        # Test overview
        overview = client.get_overview()
        assert overview['rabbitmq_version'] == '3.8.0'
        assert overview['cluster_name'] == 'test-cluster'
        print("✓ Overview data fetched successfully")
        
        # Test health check
        assert client.health_check() == True
        print("✓ Health check passed")
    
    # Test error handling
    with patch('requests.Session.get', side_effect=Exception("Connection failed")):
        client = RabbitMQClient(
            url="http://localhost:15672",
            username="guest",
            password="guest"
        )
        
        try:
            client.get_overview()
            assert False, "Should have raised exception"
        except RabbitMQAPIError:
            print("✓ Error handling works correctly")
        
        assert client.health_check() == False
        print("✓ Health check fails correctly on error")


def test_metrics_collector():
    """Test metrics collector"""
    print("\nTesting metrics collector...")
    
    # Create mock client
    mock_client = Mock()
    
    # Mock overview data
    mock_client.get_overview.return_value = {
        'rabbitmq_version': '3.8.0',
        'erlang_version': '22.0',
        'cluster_name': 'test-cluster',
        'message_stats': {
            'publish': 1000,
            'deliver_get': 900,
            'ack': 850
        },
        'queue_totals': {
            'messages': 50,
            'messages_ready': 30,
            'messages_unacknowledged': 20
        },
        'object_totals': {
            'connections': 5,
            'channels': 10,
            'queues': 8,
            'consumers': 12
        }
    }
    
    # Mock nodes data
    mock_client.get_nodes.return_value = [
        {
            'name': 'rabbit@test-node',
            'running': True,
            'mem_used': 1024000,
            'disk_free': 5000000000,
            'fd_used': 50,
            'fd_total': 1024,
            'sockets_used': 10,
            'sockets_total': 829
        }
    ]
    
    # Mock queues data
    mock_client.get_queues.return_value = [
        {
            'name': 'test-queue',
            'vhost': '/',
            'messages': 25,
            'messages_ready': 15,
            'messages_unacknowledged': 10,
            'consumers': 2,
            'memory': 512000,
            'message_stats': {
                'publish': 500,
                'deliver_get': 450,
                'ack': 425
            }
        }
    ]
    
    # Mock other endpoints
    mock_client.get_exchanges.return_value = []
    mock_client.get_connections.return_value = []
    mock_client.get_channels.return_value = []
    mock_client.get_consumers.return_value = []
    
    # Create collector
    enabled_metrics = {
        'overview': True,
        'nodes': True,
        'queues': True,
        'exchanges': True,
        'connections': True,
        'channels': True,
        'consumers': True
    }
    
    collector = RabbitMQCollector(mock_client, enabled_metrics)
    
    # Collect metrics
    metrics = list(collector.collect())
    
    assert len(metrics) > 0
    print(f"✓ Collected {len(metrics)} metrics")
    
    # Check for specific metrics
    metric_names = [m.name for m in metrics]
    
    expected_metrics = [
        'rabbitmq_version_info',
        'rabbitmq_messages_published_total',
        'rabbitmq_messages_ready',
        'rabbitmq_node_running',
        'rabbitmq_queue_messages'
    ]
    
    for expected in expected_metrics:
        assert any(expected in name for name in metric_names), f"Missing metric: {expected}"
    
    print("✓ All expected metrics found")
    
    # Test with disabled metrics
    enabled_metrics['overview'] = False
    collector = RabbitMQCollector(mock_client, enabled_metrics)
    metrics = list(collector.collect())
    
    metric_names = [m.name for m in metrics]
    assert not any('rabbitmq_messages_published_total' in name for name in metric_names)
    print("✓ Metric disabling works correctly")


def test_config_validation():
    """Test configuration validation"""
    print("\nTesting configuration validation...")
    
    # Test invalid port
    try:
        config_loader = ConfigLoader()
        config = config_loader.load()
        config.exporter.port = 99999  # Invalid port
        config_loader.config = config
        config_loader._validate_config()
        assert False, "Should have raised exception for invalid port"
    except ValueError as e:
        assert "Invalid port" in str(e)
        print("✓ Port validation works")
    
    # Test invalid interval
    try:
        config_loader = ConfigLoader()
        config = config_loader.load()
        config.exporter.interval = -1  # Invalid interval
        config_loader.config = config
        config_loader._validate_config()
        assert False, "Should have raised exception for invalid interval"
    except ValueError as e:
        assert "Invalid interval" in str(e)
        print("✓ Interval validation works")
    
    # Test invalid log level
    try:
        config_loader = ConfigLoader()
        config = config_loader.load()
        config.exporter.log_level = "INVALID"  # Invalid log level
        config_loader.config = config
        config_loader._validate_config()
        assert False, "Should have raised exception for invalid log level"
    except ValueError as e:
        assert "Invalid log level" in str(e)
        print("✓ Log level validation works")


def run_all_tests():
    """Run all tests"""
    print("RabbitMQ Exporter - Test Suite")
    print("=" * 50)
    
    try:
        test_config_loading()
        test_rabbitmq_client()
        test_metrics_collector()
        test_config_validation()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)

"""
Configuration Management for RabbitMQ Exporter
Handles configuration loading from files and environment variables.
"""

import os
import yaml
import structlog
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

logger = structlog.get_logger()


@dataclass
class RabbitMQConfig:
    """RabbitMQ connection configuration"""
    url: str = "http://localhost:15672"
    username: str = "guest"
    password: str = "guest"
    timeout: int = 10
    verify_ssl: bool = True


@dataclass
class ExporterConfig:
    """Exporter configuration"""
    port: int = 9419
    interval: int = 15
    log_level: str = "INFO"


@dataclass
class MetricsConfig:
    """Metrics collection configuration"""
    overview: bool = True
    nodes: bool = True
    queues: bool = True
    exchanges: bool = True
    connections: bool = True
    channels: bool = True
    consumers: bool = True


@dataclass
class WeChatAlertConfig:
    """WeChat alert configuration"""
    enabled: bool = False
    webhook_url: str = ""
    alert_threshold: int = 3
    alert_interval: int = 300
    send_startup_notification: bool = True
    send_shutdown_notification: bool = True


@dataclass
class AlertsConfig:
    """Alerts configuration"""
    wechat: WeChatAlertConfig = field(default_factory=WeChatAlertConfig)


@dataclass
class Config:
    """Main configuration class"""
    rabbitmq: RabbitMQConfig = field(default_factory=RabbitMQConfig)
    exporter: ExporterConfig = field(default_factory=ExporterConfig)
    metrics: MetricsConfig = field(default_factory=MetricsConfig)
    alerts: AlertsConfig = field(default_factory=AlertsConfig)


class ConfigLoader:
    """Configuration loader with support for files and environment variables"""
    
    ENV_PREFIX = "RABBITMQ_EXPORTER_"
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration loader
        
        Args:
            config_file: Path to configuration file (optional)
        """
        self.config_file = config_file
        self.config = Config()
    
    def load(self) -> Config:
        """
        Load configuration from file and environment variables
        
        Returns:
            Loaded configuration
        """
        # Load from file first
        if self.config_file and os.path.exists(self.config_file):
            self._load_from_file()
        
        # Override with environment variables
        self._load_from_env()
        
        # Validate configuration
        self._validate_config()
        
        logger.info("Configuration loaded", 
                   rabbitmq_url=self.config.rabbitmq.url,
                   exporter_port=self.config.exporter.port,
                   log_level=self.config.exporter.log_level)
        
        return self.config
    
    def _load_from_file(self):
        """Load configuration from YAML file"""
        try:
            logger.info("Loading configuration from file", file=self.config_file)
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not data:
                logger.warning("Configuration file is empty")
                return
            
            # Load RabbitMQ configuration
            if 'rabbitmq' in data:
                rabbitmq_data = data['rabbitmq']
                self.config.rabbitmq.url = rabbitmq_data.get('url', self.config.rabbitmq.url)
                self.config.rabbitmq.username = rabbitmq_data.get('username', self.config.rabbitmq.username)
                self.config.rabbitmq.password = rabbitmq_data.get('password', self.config.rabbitmq.password)
                self.config.rabbitmq.timeout = rabbitmq_data.get('timeout', self.config.rabbitmq.timeout)
                self.config.rabbitmq.verify_ssl = rabbitmq_data.get('verify_ssl', self.config.rabbitmq.verify_ssl)
            
            # Load exporter configuration
            if 'exporter' in data:
                exporter_data = data['exporter']
                self.config.exporter.port = exporter_data.get('port', self.config.exporter.port)
                self.config.exporter.interval = exporter_data.get('interval', self.config.exporter.interval)
                self.config.exporter.log_level = exporter_data.get('log_level', self.config.exporter.log_level)
            
            # Load metrics configuration
            if 'metrics' in data:
                metrics_data = data['metrics']
                self.config.metrics.overview = metrics_data.get('overview', self.config.metrics.overview)
                self.config.metrics.nodes = metrics_data.get('nodes', self.config.metrics.nodes)
                self.config.metrics.queues = metrics_data.get('queues', self.config.metrics.queues)
                self.config.metrics.exchanges = metrics_data.get('exchanges', self.config.metrics.exchanges)
                self.config.metrics.connections = metrics_data.get('connections', self.config.metrics.connections)
                self.config.metrics.channels = metrics_data.get('channels', self.config.metrics.channels)
                self.config.metrics.consumers = metrics_data.get('consumers', self.config.metrics.consumers)

            # Load alerts configuration
            if 'alerts' in data:
                alerts_data = data['alerts']
                if 'wechat' in alerts_data:
                    wechat_data = alerts_data['wechat']
                    self.config.alerts.wechat.enabled = wechat_data.get('enabled', self.config.alerts.wechat.enabled)
                    self.config.alerts.wechat.webhook_url = wechat_data.get('webhook_url', self.config.alerts.wechat.webhook_url)
                    self.config.alerts.wechat.alert_threshold = wechat_data.get('alert_threshold', self.config.alerts.wechat.alert_threshold)
                    self.config.alerts.wechat.alert_interval = wechat_data.get('alert_interval', self.config.alerts.wechat.alert_interval)
                    self.config.alerts.wechat.send_startup_notification = wechat_data.get('send_startup_notification', self.config.alerts.wechat.send_startup_notification)
                    self.config.alerts.wechat.send_shutdown_notification = wechat_data.get('send_shutdown_notification', self.config.alerts.wechat.send_shutdown_notification)
            
            logger.info("Configuration loaded from file successfully")
            
        except Exception as e:
            logger.error("Failed to load configuration from file", 
                        file=self.config_file, error=str(e))
            raise
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        logger.debug("Loading configuration from environment variables")
        
        # RabbitMQ configuration
        self.config.rabbitmq.url = self._get_env_var('RABBITMQ_URL', self.config.rabbitmq.url)
        self.config.rabbitmq.username = self._get_env_var('RABBITMQ_USERNAME', self.config.rabbitmq.username)
        self.config.rabbitmq.password = self._get_env_var('RABBITMQ_PASSWORD', self.config.rabbitmq.password)
        self.config.rabbitmq.timeout = int(self._get_env_var('RABBITMQ_TIMEOUT', str(self.config.rabbitmq.timeout)))
        self.config.rabbitmq.verify_ssl = self._get_env_bool('RABBITMQ_VERIFY_SSL', self.config.rabbitmq.verify_ssl)
        
        # Exporter configuration
        self.config.exporter.port = int(self._get_env_var('EXPORTER_PORT', str(self.config.exporter.port)))
        self.config.exporter.interval = int(self._get_env_var('EXPORTER_INTERVAL', str(self.config.exporter.interval)))
        self.config.exporter.log_level = self._get_env_var('EXPORTER_LOG_LEVEL', self.config.exporter.log_level)
        
        # Metrics configuration
        self.config.metrics.overview = self._get_env_bool('METRICS_OVERVIEW', self.config.metrics.overview)
        self.config.metrics.nodes = self._get_env_bool('METRICS_NODES', self.config.metrics.nodes)
        self.config.metrics.queues = self._get_env_bool('METRICS_QUEUES', self.config.metrics.queues)
        self.config.metrics.exchanges = self._get_env_bool('METRICS_EXCHANGES', self.config.metrics.exchanges)
        self.config.metrics.connections = self._get_env_bool('METRICS_CONNECTIONS', self.config.metrics.connections)
        self.config.metrics.channels = self._get_env_bool('METRICS_CHANNELS', self.config.metrics.channels)
        self.config.metrics.consumers = self._get_env_bool('METRICS_CONSUMERS', self.config.metrics.consumers)

        # Alerts configuration
        self.config.alerts.wechat.enabled = self._get_env_bool('ALERTS_WECHAT_ENABLED', self.config.alerts.wechat.enabled)
        self.config.alerts.wechat.webhook_url = self._get_env_var('ALERTS_WECHAT_WEBHOOK_URL', self.config.alerts.wechat.webhook_url)
        self.config.alerts.wechat.alert_threshold = int(self._get_env_var('ALERTS_WECHAT_ALERT_THRESHOLD', str(self.config.alerts.wechat.alert_threshold)))
        self.config.alerts.wechat.alert_interval = int(self._get_env_var('ALERTS_WECHAT_ALERT_INTERVAL', str(self.config.alerts.wechat.alert_interval)))
        self.config.alerts.wechat.send_startup_notification = self._get_env_bool('ALERTS_WECHAT_SEND_STARTUP_NOTIFICATION', self.config.alerts.wechat.send_startup_notification)
        self.config.alerts.wechat.send_shutdown_notification = self._get_env_bool('ALERTS_WECHAT_SEND_SHUTDOWN_NOTIFICATION', self.config.alerts.wechat.send_shutdown_notification)
    
    def _get_env_var(self, name: str, default: str) -> str:
        """Get environment variable with prefix"""
        env_name = f"{self.ENV_PREFIX}{name}"
        value = os.getenv(env_name, default)
        if value != default:
            logger.debug("Environment variable loaded", name=env_name, value=value)
        return value
    
    def _get_env_bool(self, name: str, default: bool) -> bool:
        """Get boolean environment variable with prefix"""
        env_name = f"{self.ENV_PREFIX}{name}"
        value = os.getenv(env_name)
        if value is None:
            return default
        
        bool_value = value.lower() in ('true', '1', 'yes', 'on')
        logger.debug("Boolean environment variable loaded", name=env_name, value=bool_value)
        return bool_value
    
    def _validate_config(self):
        """Validate configuration values"""
        # Validate port range
        if not (1 <= self.config.exporter.port <= 65535):
            raise ValueError(f"Invalid port number: {self.config.exporter.port}")
        
        # Validate interval
        if self.config.exporter.interval <= 0:
            raise ValueError(f"Invalid interval: {self.config.exporter.interval}")
        
        # Validate timeout
        if self.config.rabbitmq.timeout <= 0:
            raise ValueError(f"Invalid timeout: {self.config.rabbitmq.timeout}")
        
        # Validate log level
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        if self.config.exporter.log_level.upper() not in valid_log_levels:
            raise ValueError(f"Invalid log level: {self.config.exporter.log_level}")
        
        logger.debug("Configuration validation passed")
    
    def get_enabled_metrics(self) -> Dict[str, bool]:
        """Get dictionary of enabled metrics"""
        return {
            'overview': self.config.metrics.overview,
            'nodes': self.config.metrics.nodes,
            'queues': self.config.metrics.queues,
            'exchanges': self.config.metrics.exchanges,
            'connections': self.config.metrics.connections,
            'channels': self.config.metrics.channels,
            'consumers': self.config.metrics.consumers
        }

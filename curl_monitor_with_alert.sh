#!/bin/bash

# ███████ 配置区域 ███████
# 多个监控目标配置 - 格式: "名称|URL|请求方法|请求体|请求头"
MONITOR_TARGETS=(
    "EasyView主站|http://sp.easyview.com.hk|POST|{\"status\": \"ok\"}|Content-Type: application/json"
    "百度首页|https://www.baidu.com|GET||"
    "谷歌首页|https://www.google.com|GET||"
    "本地服务|http://localhost:8080/health|GET||"
)

INTERVAL=5                            # 检查间隔（秒）
LOG_FILE="curl_monitor_multi.log"    # 日志路径（确保有写入权限）
MAX_LOG_SIZE="10M"                    # 单个日志最大大小（触发轮转）

# ███████ 企业微信告警配置 ███████
WECHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3984cb17-7e35-4d55-b7f8-77b7a22f7d81"  # 替换为实际的webhook地址
ALERT_THRESHOLD=1                     # 连续失败多少次后发送告警
ALERT_INTERVAL=300                    # 告警间隔（秒），避免频繁发送
HOSTNAME=$(hostname)                  # 主机名

# 为每个目标维护独立的状态
declare -A LAST_ALERT_TIME_MAP
declare -A CONSECUTIVE_FAILURES_MAP

# ███████ 企业微信告警函数 ███████
send_wechat_alert() {
    local alert_type="$1"
    local target_name="$2"
    local target_url="$3"
    local message="$4"
    local failures="$5"
    local current_time=$(date +%s)

    # 检查告警间隔，避免频繁发送
    local last_alert_key="${target_name}_${alert_type}"
    local last_alert_time=${LAST_ALERT_TIME_MAP[$last_alert_key]:-0}

    if [ $((current_time - last_alert_time)) -lt $ALERT_INTERVAL ]; then
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] Alert suppressed for $target_name due to interval limit" | tee -a "$LOG_FILE"
        return
    fi
    
    # 构造告警消息
    local alert_message=""
    case "$alert_type" in
        "failure")
            alert_message="🚨 **服务监控告警** 🚨
**告警类型**: 连接异常
**服务名称**: ${target_name}
**主机名**: ${HOSTNAME}
**监控URL**: ${target_url}
**连续失败次数**: ${failures}
**详细信息**: ${message}
**时间**: $(date +'%Y-%m-%d %H:%M:%S')
**建议**: 请立即检查服务状态和网络连接"
            ;;
        "recovery")
            alert_message="✅ **服务监控恢复** ✅
**服务名称**: ${target_name}
**主机名**: ${HOSTNAME}
**监控URL**: ${target_url}
**状态**: 服务已恢复正常
**时间**: $(date +'%Y-%m-%d %H:%M:%S')"
            ;;
    esac
    
    # 发送企业微信消息
    local json_payload=$(cat <<EOF
{
    "msgtype": "markdown",
    "markdown": {
        "content": "${alert_message}"
    }
}
EOF
)
    
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Sending WeChat alert..." | tee -a "$LOG_FILE"
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$json_payload" \
        "$WECHAT_WEBHOOK_URL" 2>&1)
    
    if [ $? -eq 0 ]; then
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] WeChat alert sent successfully for $target_name: $response" | tee -a "$LOG_FILE"
        LAST_ALERT_TIME_MAP[$last_alert_key]=$current_time
    else
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] Failed to send WeChat alert for $target_name: $response" | tee -a "$LOG_FILE"
    fi
}

# ███████ 日志管理函数 ███████
rotate_log() {
  if [ -f "$LOG_FILE" ] && [ $(stat -c %s "$LOG_FILE") -gt $(numfmt --from=iec $MAX_LOG_SIZE) ]; then
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Rotating log file" >> "$LOG_FILE"
    gzip -c "$LOG_FILE" > "${LOG_FILE}.$(date +%Y%m%d%H%M%S).gz"
    > "$LOG_FILE"
  fi
}

# ███████ 解析目标配置函数 ███████
parse_target_config() {
    local config="$1"
    local name=$(echo "$config" | cut -d'|' -f1)
    local url=$(echo "$config" | cut -d'|' -f2)
    local method=$(echo "$config" | cut -d'|' -f3)
    local body=$(echo "$config" | cut -d'|' -f4)
    local header=$(echo "$config" | cut -d'|' -f5)

    # 设置默认值
    if [ -z "$method" ]; then
        method="GET"
    fi

    echo "$name|$url|$method|$body|$header"
}

# ███████ 单个目标健康检查函数 ███████
check_single_target() {
    local target_config="$1"
    local parsed_config=$(parse_target_config "$target_config")

    local target_name=$(echo "$parsed_config" | cut -d'|' -f1)
    local target_url=$(echo "$parsed_config" | cut -d'|' -f2)
    local request_method=$(echo "$parsed_config" | cut -d'|' -f3)
    local request_body=$(echo "$parsed_config" | cut -d'|' -f4)
    local request_header=$(echo "$parsed_config" | cut -d'|' -f5)

    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    echo "[${timestamp}] [$target_name] start #######" | tee -a "$LOG_FILE"
    echo "[${timestamp}] [$target_name] $request_method to ${target_url}..." | tee -a "$LOG_FILE"

    # 构建curl命令
    local curl_cmd="curl -s -o /dev/null -w %{http_code} --connect-timeout 10 --max-time 30"

    # 添加请求方法
    if [ "$request_method" != "GET" ]; then
        curl_cmd="$curl_cmd -X $request_method"
    fi

    # 添加请求头
    if [ -n "$request_header" ]; then
        curl_cmd="$curl_cmd -H \"$request_header\""
    fi

    # 添加请求体
    if [ -n "$request_body" ]; then
        curl_cmd="$curl_cmd -d '$request_body'"
    fi

    # 添加URL
    curl_cmd="$curl_cmd \"$target_url\""

    # 执行请求
    local curl_output=$(eval $curl_cmd 2>&1)
    local curl_exit_code=$?
    
    # 获取详细连接信息
    local detailed_info=""
    if [ "$request_method" = "GET" ]; then
        detailed_info=$(curl -s -o /dev/null \
            --connect-timeout 10 \
            --max-time 30 \
            -w "Connect: %{time_connect}s | IP: %{remote_ip} | DNS: %{time_namelookup}s | Total: %{time_total}s" \
            "$target_url" 2>/dev/null)
    fi

    echo "[${timestamp}] [$target_name] end #######" | tee -a "$LOG_FILE"

    # 获取当前目标的失败计数
    local current_failures=${CONSECUTIVE_FAILURES_MAP[$target_name]:-0}

    # 判断请求是否成功
    local is_success=0
    if [ $curl_exit_code -eq 0 ] && echo "$curl_output" | grep -q '^[0-9]\+$' && [ "$curl_output" -ge 200 ] && [ "$curl_output" -lt 400 ]; then
        is_success=1
    fi

    if [ $is_success -eq 1 ]; then
        # 请求成功
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] [$target_name] ✅ Status: HTTP $curl_output | $detailed_info" | tee -a "$LOG_FILE"

        # 如果之前有连续失败，现在恢复了，发送恢复通知
        if [ $current_failures -ge $ALERT_THRESHOLD ]; then
            send_wechat_alert "recovery" "$target_name" "$target_url" "Service recovered after $current_failures failures" "$current_failures"
        fi

        # 重置失败计数
        CONSECUTIVE_FAILURES_MAP[$target_name]=0
        return 0
    else
        # 请求失败
        current_failures=$((current_failures + 1))
        CONSECUTIVE_FAILURES_MAP[$target_name]=$current_failures
        local error_msg="HTTP $curl_output (Exit code: $curl_exit_code)"

        echo "[$(date +'%Y-%m-%d %H:%M:%S')] [$target_name] ❌ Failed: $error_msg | Consecutive failures: $current_failures" | tee -a "$LOG_FILE"

        # 达到告警阈值时发送告警
        if [ $current_failures -eq $ALERT_THRESHOLD ]; then
            send_wechat_alert "failure" "$target_name" "$target_url" "$error_msg" "$current_failures"
        elif [ $current_failures -gt $ALERT_THRESHOLD ] && [ $((current_failures % 10)) -eq 0 ]; then
            # 每10次失败发送一次提醒
            send_wechat_alert "failure" "$target_name" "$target_url" "$error_msg (Still failing after $current_failures attempts)" "$current_failures"
        fi

        return 1
    fi
}

# ███████ 多目标健康检查函数 ███████
check_all_targets() {
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    echo "[${timestamp}] ========== Starting health check cycle ==========" | tee -a "$LOG_FILE"

    local total_targets=${#MONITOR_TARGETS[@]}
    local failed_targets=0

    for target_config in "${MONITOR_TARGETS[@]}"; do
        if ! check_single_target "$target_config"; then
            failed_targets=$((failed_targets + 1))
        fi

        # 在目标之间添加小延迟，避免同时发起大量请求
        sleep 1
    done

    echo "[${timestamp}] ========== Health check cycle completed: $((total_targets - failed_targets))/$total_targets targets healthy ==========" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
}
}

# ███████ 信号处理函数 ███████
cleanup() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Monitoring stopped by signal" | tee -a "$LOG_FILE"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# ███████ 启动信息 ███████
echo "========================================" | tee -a "$LOG_FILE"
echo "[$(date +'%Y-%m-%d %H:%M:%S')] Starting multi-target service monitoring..." | tee -a "$LOG_FILE"
echo "Total targets: ${#MONITOR_TARGETS[@]}" | tee -a "$LOG_FILE"
echo "Check interval: ${INTERVAL}s" | tee -a "$LOG_FILE"
echo "Alert threshold: $ALERT_THRESHOLD failures" | tee -a "$LOG_FILE"
echo "Hostname: $HOSTNAME" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# 显示所有监控目标
echo "Monitoring targets:" | tee -a "$LOG_FILE"
for i in "${!MONITOR_TARGETS[@]}"; do
    local target_config="${MONITOR_TARGETS[$i]}"
    local parsed_config=$(parse_target_config "$target_config")
    local target_name=$(echo "$parsed_config" | cut -d'|' -f1)
    local target_url=$(echo "$parsed_config" | cut -d'|' -f2)
    local request_method=$(echo "$parsed_config" | cut -d'|' -f3)

    echo "  $((i+1)). [$target_name] $request_method $target_url" | tee -a "$LOG_FILE"

    # 初始化失败计数
    CONSECUTIVE_FAILURES_MAP[$target_name]=0
done

echo "========================================" | tee -a "$LOG_FILE"

# ███████ 主监控循环 ███████
while true; do
    check_all_targets

    # 日志轮转检查
    rotate_log

    sleep $INTERVAL
done

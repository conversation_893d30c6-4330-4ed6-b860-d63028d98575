#!/bin/bash

# ███████ 配置区域 ███████
URL="http://sp.easyview.com.hk"
INTERVAL=1
LOG_FILE="/var/log/curl_monitor_slb.log"  # 日志路径（确保有写入权限）
MAX_LOG_SIZE="10M"                    # 单个日志最大大小（触发轮转）
REQUEST_BODY='{"status": "ok"}'
REQUEST_HEADER="Content-Type: application/json"

# ███████ 企业微信告警配置 ███████
WECHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY"  # 替换为实际的webhook地址
ALERT_THRESHOLD=1                     # 连续失败多少次后发送告警
ALERT_INTERVAL=300                    # 告警间隔（秒），避免频繁发送
LAST_ALERT_TIME=0                     # 上次告警时间
CONSECUTIVE_FAILURES=0                # 连续失败次数
HOSTNAME=$(hostname)                  # 主机名

# ███████ 企业微信告警函数 ███████
send_wechat_alert() {
    local alert_type="$1"
    local message="$2"
    local current_time=$(date +%s)
    
    # 检查告警间隔，避免频繁发送
    if [ $((current_time - LAST_ALERT_TIME)) -lt $ALERT_INTERVAL ]; then
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] Alert suppressed due to interval limit" | tee -a "$LOG_FILE"
        return
    fi
    
    # 构造告警消息
    local alert_message=""
    case "$alert_type" in
        "failure")
            alert_message="🚨 **服务监控告警** 🚨
**告警类型**: 连接异常
**主机名**: ${HOSTNAME}
**监控URL**: ${URL}
**连续失败次数**: ${CONSECUTIVE_FAILURES}
**详细信息**: ${message}
**时间**: $(date +'%Y-%m-%d %H:%M:%S')
**建议**: 请立即检查服务状态和网络连接"
            ;;
        "recovery")
            alert_message="✅ **服务监控恢复** ✅
**主机名**: ${HOSTNAME}
**监控URL**: ${URL}
**状态**: 服务已恢复正常
**时间**: $(date +'%Y-%m-%d %H:%M:%S')"
            ;;
    esac
    
    # 发送企业微信消息
    local json_payload=$(cat <<EOF
{
    "msgtype": "markdown",
    "markdown": {
        "content": "${alert_message}"
    }
}
EOF
)
    
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Sending WeChat alert..." | tee -a "$LOG_FILE"
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$json_payload" \
        "$WECHAT_WEBHOOK_URL" 2>&1)
    
    if [ $? -eq 0 ]; then
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] WeChat alert sent successfully: $response" | tee -a "$LOG_FILE"
        LAST_ALERT_TIME=$current_time
    else
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] Failed to send WeChat alert: $response" | tee -a "$LOG_FILE"
    fi
}

# ███████ 日志管理函数 ███████
rotate_log() {
  if [ -f "$LOG_FILE" ] && [ $(stat -c %s "$LOG_FILE") -gt $(numfmt --from=iec $MAX_LOG_SIZE) ]; then
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Rotating log file" >> "$LOG_FILE"
    gzip -c "$LOG_FILE" > "${LOG_FILE}.$(date +%Y%m%d%H%M%S).gz"
    > "$LOG_FILE"
  fi
}

# ███████ 健康检查函数 ███████
check_service_health() {
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    echo "[${timestamp}] start #######" | tee -a "$LOG_FILE"
    echo "[${timestamp}] POST to ${URL}... " | tee -a "$LOG_FILE"
    
    # 执行请求并记录完整信息到日志
    local curl_output=$(curl -s -o /dev/null -w "%{http_code}" \
        -X POST \
        -H "$REQUEST_HEADER" \
        -d "$REQUEST_BODY" \
        --connect-timeout 10 \
        --max-time 30 \
        "$URL" 2>&1)
    
    local curl_exit_code=$?
    local http_code=""
    
    # 获取详细的连接信息
    local detailed_info=$(curl -s -o /dev/null \
        -X POST \
        -H "$REQUEST_HEADER" \
        -d "$REQUEST_BODY" \
        --connect-timeout 10 \
        --max-time 30 \
        -w "Connect: %{time_connect}s | IP: %{remote_ip} | Port: %{local_port} | DNS: %{time_namelookup}s | Total: %{time_total}s" \
        "$URL" 2>/dev/null)
    
    echo "[${timestamp}] end #######" | tee -a "$LOG_FILE"
    
    # 判断请求是否成功
    if [ $curl_exit_code -eq 0 ] && [[ "$curl_output" =~ ^[0-9]+$ ]] && [ "$curl_output" -ge 200 ] && [ "$curl_output" -lt 400 ]; then
        # 请求成功
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] ✅ Status: HTTP $curl_output | $detailed_info" | tee -a "$LOG_FILE"
        
        # 如果之前有连续失败，现在恢复了，发送恢复通知
        if [ $CONSECUTIVE_FAILURES -ge $ALERT_THRESHOLD ]; then
            send_wechat_alert "recovery" "Service recovered after $CONSECUTIVE_FAILURES failures"
        fi
        
        CONSECUTIVE_FAILURES=0
        return 0
    else
        # 请求失败
        CONSECUTIVE_FAILURES=$((CONSECUTIVE_FAILURES + 1))
        local error_msg="HTTP $curl_output (Exit code: $curl_exit_code)"
        
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] ❌ Failed: $error_msg | Consecutive failures: $CONSECUTIVE_FAILURES" | tee -a "$LOG_FILE"
        
        # 达到告警阈值时发送告警
        if [ $CONSECUTIVE_FAILURES -eq $ALERT_THRESHOLD ]; then
            send_wechat_alert "failure" "$error_msg"
        elif [ $CONSECUTIVE_FAILURES -gt $ALERT_THRESHOLD ] && [ $((CONSECUTIVE_FAILURES % 10)) -eq 0 ]; then
            # 每10次失败发送一次提醒
            send_wechat_alert "failure" "$error_msg (Still failing after $CONSECUTIVE_FAILURES attempts)"
        fi
        
        return 1
    fi
}

# ███████ 信号处理函数 ███████
cleanup() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Monitoring stopped by signal" | tee -a "$LOG_FILE"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# ███████ 启动信息 ███████
echo "========================================" | tee -a "$LOG_FILE"
echo "[$(date +'%Y-%m-%d %H:%M:%S')] Starting service monitoring..." | tee -a "$LOG_FILE"
echo "Target URL: $URL" | tee -a "$LOG_FILE"
echo "Check interval: ${INTERVAL}s" | tee -a "$LOG_FILE"
echo "Alert threshold: $ALERT_THRESHOLD failures" | tee -a "$LOG_FILE"
echo "Hostname: $HOSTNAME" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"

# ███████ 主监控循环 ███████
while true; do
    check_service_health
    
    # 日志轮转检查
    rotate_log
    
    sleep $INTERVAL
done

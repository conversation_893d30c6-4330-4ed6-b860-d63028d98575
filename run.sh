#!/bin/bash

# RabbitMQ Exporter Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
CONFIG_FILE="config.yaml"
PYTHON_CMD="python3"
VENV_DIR="venv"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to setup virtual environment
setup_venv() {
    if [ ! -d "$VENV_DIR" ]; then
        print_status "Creating virtual environment..."
        $PYTHON_CMD -m venv $VENV_DIR
    fi
    
    print_status "Activating virtual environment..."
    source $VENV_DIR/bin/activate
    
    print_status "Installing dependencies..."
    pip install -r requirements.txt
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    $PYTHON_CMD test_exporter.py
    if [ $? -eq 0 ]; then
        print_status "All tests passed!"
    else
        print_error "Tests failed!"
        exit 1
    fi
}

# Function to start the exporter
start_exporter() {
    print_status "Starting RabbitMQ Exporter..."
    
    # Check if config file exists
    if [ ! -f "$CONFIG_FILE" ]; then
        print_warning "Config file $CONFIG_FILE not found, using defaults"
        CONFIG_ARG=""
    else
        CONFIG_ARG="--config $CONFIG_FILE"
    fi
    
    # Start the exporter
    exec $PYTHON_CMD main.py $CONFIG_ARG "$@"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start     Start the RabbitMQ Exporter (default)"
    echo "  test      Run tests"
    echo "  setup     Setup virtual environment and install dependencies"
    echo "  example   Run usage examples"
    echo "  help      Show this help message"
    echo ""
    echo "Options:"
    echo "  --config FILE     Configuration file (default: config.yaml)"
    echo "  --venv DIR        Virtual environment directory (default: venv)"
    echo "  --python CMD      Python command (default: python3)"
    echo ""
    echo "Examples:"
    echo "  $0 start --config my-config.yaml"
    echo "  $0 test"
    echo "  $0 setup --python python3.9"
}

# Parse command line arguments
COMMAND="start"
while [[ $# -gt 0 ]]; do
    case $1 in
        start|test|setup|example|help)
            COMMAND="$1"
            shift
            ;;
        --config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --venv)
            VENV_DIR="$2"
            shift 2
            ;;
        --python)
            PYTHON_CMD="$2"
            shift 2
            ;;
        -h|--help)
            COMMAND="help"
            shift
            ;;
        *)
            # Pass remaining arguments to the exporter
            break
            ;;
    esac
done

# Check if Python is available
if ! command_exists $PYTHON_CMD; then
    print_error "Python command '$PYTHON_CMD' not found!"
    print_error "Please install Python 3.7+ or specify correct command with --python"
    exit 1
fi

# Execute command
case $COMMAND in
    start)
        print_status "RabbitMQ Exporter Startup"
        print_status "========================="
        
        # Check if virtual environment should be used
        if [ -d "$VENV_DIR" ]; then
            print_status "Using virtual environment: $VENV_DIR"
            source $VENV_DIR/bin/activate
        else
            print_warning "Virtual environment not found. Run '$0 setup' to create one."
        fi
        
        start_exporter "$@"
        ;;
    
    test)
        print_status "Running RabbitMQ Exporter Tests"
        print_status "==============================="
        
        if [ -d "$VENV_DIR" ]; then
            source $VENV_DIR/bin/activate
        fi
        
        run_tests
        ;;
    
    setup)
        print_status "Setting up RabbitMQ Exporter"
        print_status "============================"
        setup_venv
        print_status "Setup completed successfully!"
        ;;
    
    example)
        print_status "Running RabbitMQ Exporter Examples"
        print_status "=================================="
        
        if [ -d "$VENV_DIR" ]; then
            source $VENV_DIR/bin/activate
        fi
        
        $PYTHON_CMD example_usage.py
        ;;
    
    help)
        show_usage
        ;;
    
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac

#!/bin/bash

# 测试脚本兼容性

echo "Testing shell compatibility..."
echo "Shell: $0"
echo "Bash version: ${BASH_VERSION:-Not available}"

# 测试数字检查函数
is_number() {
    case $1 in
        ''|*[!0-9]*) return 1 ;;
        *) return 0 ;;
    esac
}

# 测试用例
test_cases=("200" "404" "abc" "123abc" "" "500")

echo ""
echo "Testing is_number function:"
for test in "${test_cases[@]}"; do
    if is_number "$test"; then
        echo "✅ '$test' is a number"
    else
        echo "❌ '$test' is not a number"
    fi
done

# 测试HTTP状态码检查
echo ""
echo "Testing HTTP status code validation:"
for code in "${test_cases[@]}"; do
    if is_number "$code" && [ "$code" -ge 200 ] && [ "$code" -lt 400 ]; then
        echo "✅ HTTP $code is success"
    else
        echo "❌ HTTP $code is failure"
    fi
done

echo ""
echo "All tests completed!"

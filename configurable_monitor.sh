#!/bin/bash

# ███████ 可配置多域名监控脚本 ███████

# ███████ 默认配置 ███████
CONFIG_FILE="monitor_config.conf"
INTERVAL=10
LOG_FILE="multi_domain_monitor.log"
MAX_LOG_SIZE="50M"

# 企业微信告警配置
WECHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3984cb17-7e35-4d55-b7f8-77b7a22f7d81"
ALERT_THRESHOLD=2
ALERT_INTERVAL=300
HOSTNAME=$(hostname)

# 状态跟踪
declare -A CONSECUTIVE_FAILURES_MAP
declare -A LAST_ALERT_TIME_MAP
declare -a MONITOR_TARGETS

# ███████ 参数解析 ███████
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -c, --config FILE     Configuration file (default: monitor_config.conf)"
    echo "  -i, --interval SEC    Check interval in seconds (default: 10)"
    echo "  -l, --log FILE        Log file path (default: multi_domain_monitor.log)"
    echo "  -t, --threshold NUM   Alert threshold (default: 2)"
    echo "  -w, --webhook URL     WeChat webhook URL"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Configuration file format:"
    echo "  Each line: Name|URL|Method|Body|Header"
    echo "  Lines starting with # are comments"
    echo ""
    echo "Examples:"
    echo "  $0 -c my_config.conf -i 30"
    echo "  $0 --threshold 3 --interval 60"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -i|--interval)
            INTERVAL="$2"
            shift 2
            ;;
        -l|--log)
            LOG_FILE="$2"
            shift 2
            ;;
        -t|--threshold)
            ALERT_THRESHOLD="$2"
            shift 2
            ;;
        -w|--webhook)
            WECHAT_WEBHOOK_URL="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# ███████ 工具函数 ███████
log_message() {
    local message="$1"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" | tee -a "$LOG_FILE"
}

# 检查是否为数字
is_number() {
    case $1 in
        ''|*[!0-9]*) return 1 ;;
        *) return 0 ;;
    esac
}

# 加载配置文件
load_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_message "ERROR: Configuration file '$CONFIG_FILE' not found"
        exit 1
    fi
    
    log_message "Loading configuration from: $CONFIG_FILE"
    
    local count=0
    while IFS= read -r line; do
        # 跳过空行和注释
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # 验证格式
        if [[ "$line" =~ ^[^|]+\|[^|]+\|.*\|.*\|.*$ ]]; then
            MONITOR_TARGETS+=("$line")
            count=$((count + 1))
        else
            log_message "WARNING: Invalid config line ignored: $line"
        fi
    done < "$CONFIG_FILE"
    
    if [ $count -eq 0 ]; then
        log_message "ERROR: No valid targets found in configuration file"
        exit 1
    fi
    
    log_message "Loaded $count monitoring targets"
}

# 发送企业微信告警
send_wechat_alert() {
    local alert_type="$1"
    local target_name="$2"
    local target_url="$3"
    local message="$4"
    local failures="$5"
    local current_time=$(date +%s)
    
    # 检查告警间隔
    local alert_key="${target_name}_${alert_type}"
    local last_alert_time=${LAST_ALERT_TIME_MAP[$alert_key]:-0}
    
    if [ $((current_time - last_alert_time)) -lt $ALERT_INTERVAL ]; then
        log_message "[$target_name] Alert suppressed due to interval limit"
        return
    fi
    
    # 构造告警消息
    local alert_content=""
    if [ "$alert_type" = "failure" ]; then
        alert_content="🚨 **多域名监控告警** 🚨\\n\\n**服务名称**: ${target_name}\\n**主机名**: ${HOSTNAME}\\n**监控URL**: ${target_url}\\n**连续失败次数**: ${failures}\\n**错误信息**: ${message}\\n**时间**: $(date +'%Y-%m-%d %H:%M:%S')\\n\\n**建议**: 请检查服务状态和网络连接"
    else
        alert_content="✅ **多域名监控恢复** ✅\\n\\n**服务名称**: ${target_name}\\n**主机名**: ${HOSTNAME}\\n**监控URL**: ${target_url}\\n**状态**: 服务已恢复正常\\n**恢复时间**: $(date +'%Y-%m-%d %H:%M:%S')\\n**之前失败次数**: ${failures}"
    fi
    
    # 发送消息
    log_message "[$target_name] Sending WeChat alert..."
    
    local json_data="{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"$alert_content\"}}"
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        "$WECHAT_WEBHOOK_URL" 2>&1)
    
    if [ $? -eq 0 ]; then
        log_message "[$target_name] WeChat alert sent successfully"
        LAST_ALERT_TIME_MAP[$alert_key]=$current_time
    else
        log_message "[$target_name] Failed to send WeChat alert: $response"
    fi
}

# 检查单个目标
check_single_target() {
    local target_config="$1"
    
    # 解析配置
    local name=$(echo "$target_config" | cut -d'|' -f1)
    local url=$(echo "$target_config" | cut -d'|' -f2)
    local method=$(echo "$target_config" | cut -d'|' -f3)
    local body=$(echo "$target_config" | cut -d'|' -f4)
    local header=$(echo "$target_config" | cut -d'|' -f5)
    
    # 设置默认值
    if [ -z "$method" ]; then
        method="GET"
    fi
    
    log_message "[$name] Checking $method $url"
    
    # 构建curl命令
    local curl_cmd="curl -s -o /dev/null -w %{http_code} --connect-timeout 10 --max-time 30"
    
    if [ "$method" != "GET" ]; then
        curl_cmd="$curl_cmd -X $method"
    fi
    
    if [ -n "$header" ]; then
        curl_cmd="$curl_cmd -H \"$header\""
    fi
    
    if [ -n "$body" ]; then
        curl_cmd="$curl_cmd -d '$body'"
    fi
    
    curl_cmd="$curl_cmd \"$url\""
    
    # 执行请求
    local start_time=$(date +%s)
    local http_code=$(eval $curl_cmd 2>&1)
    local curl_exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 获取当前失败计数
    local current_failures=${CONSECUTIVE_FAILURES_MAP[$name]:-0}
    
    # 判断是否成功
    local is_success=0
    if [ $curl_exit_code -eq 0 ] && is_number "$http_code" && [ "$http_code" -ge 200 ] && [ "$http_code" -lt 400 ]; then
        is_success=1
    fi
    
    if [ $is_success -eq 1 ]; then
        # 成功
        log_message "[$name] ✅ HTTP $http_code (${duration}s)"
        
        # 如果之前有失败，发送恢复通知
        if [ $current_failures -ge $ALERT_THRESHOLD ]; then
            send_wechat_alert "recovery" "$name" "$url" "Service recovered" "$current_failures"
        fi
        
        CONSECUTIVE_FAILURES_MAP[$name]=0
        return 0
    else
        # 失败
        current_failures=$((current_failures + 1))
        CONSECUTIVE_FAILURES_MAP[$name]=$current_failures
        local error_msg="HTTP $http_code (Exit: $curl_exit_code)"
        
        log_message "[$name] ❌ $error_msg (${duration}s) - Failures: $current_failures"
        
        # 发送告警
        if [ $current_failures -eq $ALERT_THRESHOLD ]; then
            send_wechat_alert "failure" "$name" "$url" "$error_msg" "$current_failures"
        elif [ $current_failures -gt $ALERT_THRESHOLD ] && [ $((current_failures % 5)) -eq 0 ]; then
            send_wechat_alert "failure" "$name" "$url" "$error_msg (Still failing)" "$current_failures"
        fi
        
        return 1
    fi
}

# 检查所有目标
check_all_targets() {
    log_message "========== Starting monitoring cycle =========="
    
    local total_targets=${#MONITOR_TARGETS[@]}
    local healthy_targets=0
    
    for target_config in "${MONITOR_TARGETS[@]}"; do
        if check_single_target "$target_config"; then
            healthy_targets=$((healthy_targets + 1))
        fi
        
        # 目标间延迟
        sleep 1
    done
    
    log_message "========== Cycle completed: $healthy_targets/$total_targets targets healthy =========="
    echo "" >> "$LOG_FILE"
}

# 日志轮转
rotate_log() {
    if [ -f "$LOG_FILE" ]; then
        local file_size=$(wc -c < "$LOG_FILE" 2>/dev/null || echo 0)
        local max_size_bytes=52428800  # 50MB
        
        if [ "$file_size" -gt "$max_size_bytes" ]; then
            log_message "Rotating log file (size: $file_size bytes)"
            if command -v gzip >/dev/null 2>&1; then
                gzip -c "$LOG_FILE" > "${LOG_FILE}.$(date +%Y%m%d%H%M%S).gz"
            else
                cp "$LOG_FILE" "${LOG_FILE}.$(date +%Y%m%d%H%M%S).bak"
            fi
            > "$LOG_FILE"
        fi
    fi
}

# 信号处理
cleanup() {
    log_message "Monitoring stopped by signal"
    exit 0
}

trap cleanup SIGINT SIGTERM

# ███████ 主程序 ███████

# 检查必要命令
for cmd in curl date hostname; do
    if ! command -v "$cmd" >/dev/null 2>&1; then
        echo "ERROR: Required command '$cmd' not found"
        exit 1
    fi
done

# 加载配置
load_config

# 启动信息
log_message "========================================"
log_message "Configurable Multi-Domain Monitor Starting..."
log_message "Configuration file: $CONFIG_FILE"
log_message "Total targets: ${#MONITOR_TARGETS[@]}"
log_message "Check interval: ${INTERVAL}s"
log_message "Alert threshold: $ALERT_THRESHOLD failures"
log_message "Log file: $LOG_FILE"
log_message "Hostname: $HOSTNAME"
log_message ""

# 显示监控目标
log_message "Monitoring targets:"
for i in "${!MONITOR_TARGETS[@]}"; do
    local target_config="${MONITOR_TARGETS[$i]}"
    local name=$(echo "$target_config" | cut -d'|' -f1)
    local url=$(echo "$target_config" | cut -d'|' -f2)
    local method=$(echo "$target_config" | cut -d'|' -f3)
    
    if [ -z "$method" ]; then
        method="GET"
    fi
    
    log_message "  $((i+1)). [$name] $method $url"
    CONSECUTIVE_FAILURES_MAP[$name]=0
done

log_message "========================================"

# 主监控循环
while true; do
    check_all_targets
    rotate_log
    sleep $INTERVAL
done

#!/bin/bash

# 简化版监控脚本 - Ubuntu兼容

# ███████ 配置区域 ███████
URL="http://sp.easyview.com.hk"
INTERVAL=1
LOG_FILE="curl_monitor_slb.log"
REQUEST_BODY='{"status": "ok"}'
REQUEST_HEADER="Content-Type: application/json"

# 企业微信配置
WECHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3984cb17-7e35-4d55-b7f8-77b7a22f7d81"
ALERT_THRESHOLD=1
ALERT_INTERVAL=300
LAST_ALERT_TIME=0
CONSECUTIVE_FAILURES=0
HOSTNAME=$(hostname)

# ███████ 工具函数 ███████
log_message() {
    local message="$1"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" | tee -a "$LOG_FILE"
}

# 检查是否为数字 - POSIX兼容
is_number() {
    case $1 in
        ''|*[!0-9]*) return 1 ;;
        *) return 0 ;;
    esac
}

# 发送企业微信告警
send_wechat_alert() {
    local alert_type="$1"
    local message="$2"
    local current_time=$(date +%s)
    
    # 检查告警间隔
    if [ $((current_time - LAST_ALERT_TIME)) -lt $ALERT_INTERVAL ]; then
        log_message "Alert suppressed due to interval limit"
        return
    fi
    
    # 构造告警消息
    local alert_content=""
    if [ "$alert_type" = "failure" ]; then
        alert_content="🚨 **服务监控告警** 🚨\\n**主机名**: ${HOSTNAME}\\n**监控URL**: ${URL}\\n**连续失败次数**: ${CONSECUTIVE_FAILURES}\\n**详细信息**: ${message}\\n**时间**: $(date +'%Y-%m-%d %H:%M:%S')"
    else
        alert_content="✅ **服务监控恢复** ✅\\n**主机名**: ${HOSTNAME}\\n**监控URL**: ${URL}\\n**状态**: 服务已恢复正常\\n**时间**: $(date +'%Y-%m-%d %H:%M:%S')"
    fi
    
    # 发送消息
    log_message "Sending WeChat alert..."
    
    local json_data="{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"$alert_content\"}}"
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        "$WECHAT_WEBHOOK_URL" 2>&1)
    
    if [ $? -eq 0 ]; then
        log_message "WeChat alert sent successfully: $response"
        LAST_ALERT_TIME=$current_time
    else
        log_message "Failed to send WeChat alert: $response"
    fi
}

# 健康检查
check_service_health() {
    log_message "start #######"
    log_message "POST to ${URL}..."
    
    # 执行请求
    local curl_output=$(curl -s -o /dev/null -w "%{http_code}" \
        -X POST \
        -H "$REQUEST_HEADER" \
        -d "$REQUEST_BODY" \
        --connect-timeout 10 \
        --max-time 30 \
        "$URL" 2>&1)
    
    local curl_exit_code=$?
    
    log_message "end #######"
    
    # 判断请求是否成功
    local is_success=0
    if [ $curl_exit_code -eq 0 ] && is_number "$curl_output"; then
        if [ "$curl_output" -ge 200 ] && [ "$curl_output" -lt 400 ]; then
            is_success=1
        fi
    fi
    
    if [ $is_success -eq 1 ]; then
        # 请求成功
        log_message "✅ Status: HTTP $curl_output"
        
        # 如果之前有失败，发送恢复通知
        if [ $CONSECUTIVE_FAILURES -ge $ALERT_THRESHOLD ]; then
            send_wechat_alert "recovery" "Service recovered after $CONSECUTIVE_FAILURES failures"
        fi
        
        CONSECUTIVE_FAILURES=0
        return 0
    else
        # 请求失败
        CONSECUTIVE_FAILURES=$((CONSECUTIVE_FAILURES + 1))
        local error_msg="HTTP $curl_output (Exit code: $curl_exit_code)"
        
        log_message "❌ Failed: $error_msg | Consecutive failures: $CONSECUTIVE_FAILURES"
        
        # 发送告警
        if [ $CONSECUTIVE_FAILURES -eq $ALERT_THRESHOLD ]; then
            send_wechat_alert "failure" "$error_msg"
        elif [ $CONSECUTIVE_FAILURES -gt $ALERT_THRESHOLD ]; then
            # 每10次失败发送一次提醒
            local remainder=$((CONSECUTIVE_FAILURES % 10))
            if [ $remainder -eq 0 ]; then
                send_wechat_alert "failure" "$error_msg (Still failing after $CONSECUTIVE_FAILURES attempts)"
            fi
        fi
        
        return 1
    fi
}

# 信号处理
cleanup() {
    log_message "Monitoring stopped by signal"
    exit 0
}

trap cleanup SIGINT SIGTERM

# ███████ 启动信息 ███████
log_message "========================================"
log_message "Starting service monitoring..."
log_message "Target URL: $URL"
log_message "Check interval: ${INTERVAL}s"
log_message "Alert threshold: $ALERT_THRESHOLD failures"
log_message "Hostname: $HOSTNAME"
log_message "Shell: $0"
log_message "========================================"

# 检查必要命令
for cmd in curl date hostname; do
    if ! command -v "$cmd" >/dev/null 2>&1; then
        log_message "ERROR: Required command '$cmd' not found"
        exit 1
    fi
done

# ███████ 主监控循环 ███████
while true; do
    check_service_health
    sleep $INTERVAL
done

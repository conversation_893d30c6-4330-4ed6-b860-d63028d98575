#!/usr/bin/env python3
"""
RabbitMQ Exporter
Prometheus exporter for RabbitMQ metrics using the Management API.
"""

import sys
import signal
import time
import logging
import structlog
import click
from typing import Optional

from config import Config<PERSON>oader, Config
from rabbitmq_client import RabbitMQClient, RabbitMQAPIError
from metrics_collector import <PERSON><PERSON><PERSON><PERSON>oll<PERSON>
from http_server import MetricsServer


def setup_logging(log_level: str):
    """Setup structured logging"""
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper())
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


class RabbitMQExporter:
    """Main exporter application"""
    
    def __init__(self, config: Config):
        """
        Initialize the exporter
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.logger = structlog.get_logger()
        self.rabbitmq_client = None
        self.collector = None
        self.server = None
        self.running = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def start(self):
        """Start the exporter"""
        try:
            self.logger.info("Starting RabbitMQ Exporter")
            
            # Initialize RabbitMQ client
            self.rabbitmq_client = RabbitMQClient(
                url=self.config.rabbitmq.url,
                username=self.config.rabbitmq.username,
                password=self.config.rabbitmq.password,
                timeout=self.config.rabbitmq.timeout,
                verify_ssl=self.config.rabbitmq.verify_ssl
            )
            
            # Test RabbitMQ connection
            if not self.rabbitmq_client.health_check():
                raise RabbitMQAPIError("Failed to connect to RabbitMQ")
            
            # Initialize metrics collector
            enabled_metrics = {
                'overview': self.config.metrics.overview,
                'nodes': self.config.metrics.nodes,
                'queues': self.config.metrics.queues,
                'exchanges': self.config.metrics.exchanges,
                'connections': self.config.metrics.connections,
                'channels': self.config.metrics.channels,
                'consumers': self.config.metrics.consumers
            }
            
            self.collector = RabbitMQCollector(
                rabbitmq_client=self.rabbitmq_client,
                enabled_metrics=enabled_metrics
            )
            
            # Initialize and start HTTP server
            self.server = MetricsServer(
                port=self.config.exporter.port,
                rabbitmq_client=self.rabbitmq_client,
                collector=self.collector
            )
            self.server.start()
            
            self.running = True
            self.logger.info("RabbitMQ Exporter started successfully",
                           port=self.config.exporter.port,
                           rabbitmq_url=self.config.rabbitmq.url)
            
            # Main loop
            self._run_main_loop()
            
        except Exception as e:
            self.logger.error("Failed to start exporter", error=str(e))
            self.stop()
            sys.exit(1)
    
    def stop(self):
        """Stop the exporter"""
        if not self.running:
            return
        
        self.logger.info("Stopping RabbitMQ Exporter")
        self.running = False
        
        if self.server:
            self.server.stop()
        
        self.logger.info("RabbitMQ Exporter stopped")
    
    def _run_main_loop(self):
        """Main application loop"""
        try:
            while self.running:
                time.sleep(1)
                
                # Periodic health check
                if int(time.time()) % 60 == 0:  # Every minute
                    if not self.rabbitmq_client.health_check():
                        self.logger.warning("RabbitMQ health check failed")
                
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error("Error in main loop", error=str(e))
        finally:
            self.stop()
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info("Received shutdown signal", signal=signum)
        self.running = False


@click.command()
@click.option('--config', '-c', 
              type=click.Path(exists=True),
              help='Configuration file path')
@click.option('--port', '-p',
              type=int,
              help='Port to expose metrics on (overrides config)')
@click.option('--rabbitmq-url',
              help='RabbitMQ Management API URL (overrides config)')
@click.option('--rabbitmq-username',
              help='RabbitMQ username (overrides config)')
@click.option('--rabbitmq-password',
              help='RabbitMQ password (overrides config)')
@click.option('--log-level',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR'], case_sensitive=False),
              help='Log level (overrides config)')
@click.option('--version', is_flag=True,
              help='Show version and exit')
def main(config: Optional[str], port: Optional[int], rabbitmq_url: Optional[str],
         rabbitmq_username: Optional[str], rabbitmq_password: Optional[str],
         log_level: Optional[str], version: bool):
    """RabbitMQ Prometheus Exporter"""
    
    if version:
        click.echo("RabbitMQ Exporter v1.0.0")
        return
    
    try:
        # Load configuration
        config_loader = ConfigLoader(config)
        app_config = config_loader.load()
        
        # Override with command line arguments
        if port:
            app_config.exporter.port = port
        if rabbitmq_url:
            app_config.rabbitmq.url = rabbitmq_url
        if rabbitmq_username:
            app_config.rabbitmq.username = rabbitmq_username
        if rabbitmq_password:
            app_config.rabbitmq.password = rabbitmq_password
        if log_level:
            app_config.exporter.log_level = log_level
        
        # Setup logging
        setup_logging(app_config.exporter.log_level)
        
        # Start exporter
        exporter = RabbitMQExporter(app_config)
        exporter.start()
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    main()

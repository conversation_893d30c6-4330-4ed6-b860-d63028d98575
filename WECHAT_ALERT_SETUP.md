# 企业微信告警设置指南

本指南将帮助您配置RabbitMQ Exporter的企业微信告警功能。

## 1. 创建企业微信机器人

### 步骤1：创建群聊机器人

1. 在企业微信中创建一个群聊
2. 在群聊中点击右上角的"..."
3. 选择"群机器人"
4. 点击"添加机器人"
5. 输入机器人名称（如：RabbitMQ监控）
6. 复制生成的Webhook地址

### 步骤2：获取Webhook URL

Webhook URL格式如下：
```
https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY
```

其中`YOUR_WEBHOOK_KEY`是您的机器人密钥。

## 2. 配置告警

### 方法1：使用配置文件

编辑`config.yaml`文件：

```yaml
alerts:
  wechat:
    enabled: true
    webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY"
    alert_threshold: 3      # 连续失败3次后发送告警
    alert_interval: 300     # 告警间隔5分钟
    send_startup_notification: true
    send_shutdown_notification: true
```

### 方法2：使用环境变量

```bash
export RABBITMQ_EXPORTER_ALERTS_WECHAT_ENABLED=true
export RABBITMQ_EXPORTER_ALERTS_WECHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY"
export RABBITMQ_EXPORTER_ALERTS_WECHAT_ALERT_THRESHOLD=3
export RABBITMQ_EXPORTER_ALERTS_WECHAT_ALERT_INTERVAL=300
export RABBITMQ_EXPORTER_ALERTS_WECHAT_SEND_STARTUP_NOTIFICATION=true
export RABBITMQ_EXPORTER_ALERTS_WECHAT_SEND_SHUTDOWN_NOTIFICATION=true
```

## 3. 告警类型

### 连接异常告警

当RabbitMQ连接失败时发送，包含以下信息：
- 告警类型：连接异常
- 主机名
- RabbitMQ地址
- 连续失败次数
- 错误信息
- 建议操作

示例消息：
```
🚨 RabbitMQ监控告警 🚨

告警类型: 连接异常
主机名: server-01
RabbitMQ地址: http://localhost:15672
连续失败次数: 3
错误信息: Connection refused
时间: 2024-01-15 10:30:00

建议操作:
1. 检查RabbitMQ服务状态
2. 验证网络连接
3. 检查认证信息
4. 查看RabbitMQ日志
```

### 连接恢复通知

当RabbitMQ连接恢复时发送：
```
✅ RabbitMQ监控恢复 ✅

主机名: server-01
RabbitMQ地址: http://localhost:15672
状态: 服务已恢复正常
恢复时间: 2024-01-15 10:35:00
之前失败次数: 5
```

### 启动/停止通知

服务启动或停止时的通知消息。

## 4. 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `enabled` | 是否启用企业微信告警 | `false` |
| `webhook_url` | 企业微信机器人Webhook地址 | 空 |
| `alert_threshold` | 连续失败多少次后发送告警 | `3` |
| `alert_interval` | 告警间隔（秒），避免频繁发送 | `300` |
| `send_startup_notification` | 是否发送启动通知 | `true` |
| `send_shutdown_notification` | 是否发送停止通知 | `true` |

## 5. 测试告警

### 测试连接异常告警

1. 启动RabbitMQ Exporter
2. 停止RabbitMQ服务
3. 等待3次失败后应收到告警消息

### 测试恢复通知

1. 在收到告警后重启RabbitMQ服务
2. 应收到恢复通知

### 测试启动通知

```bash
python main.py --config config-with-alerts.yaml
```

## 6. 故障排除

### 常见问题

1. **收不到告警消息**
   - 检查webhook_url是否正确
   - 确认机器人已添加到群聊
   - 检查网络连接

2. **告警过于频繁**
   - 增加`alert_interval`值
   - 增加`alert_threshold`值

3. **告警延迟**
   - 检查网络延迟
   - 企业微信API可能有限流

### 调试模式

启用DEBUG日志查看详细信息：

```bash
python main.py --log-level DEBUG --config config-with-alerts.yaml
```

### 手动测试Webhook

使用curl测试webhook是否正常：

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "msgtype": "markdown",
    "markdown": {
      "content": "## 测试消息\n这是一条测试消息"
    }
  }' \
  "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY"
```

## 7. 安全建议

1. **保护Webhook密钥**
   - 不要在代码中硬编码密钥
   - 使用环境变量或安全的配置管理

2. **限制机器人权限**
   - 只添加到需要接收告警的群聊
   - 定期轮换密钥

3. **监控告警频率**
   - 设置合理的告警阈值
   - 避免告警风暴

## 8. 高级配置

### Docker环境

在docker-compose.yml中配置：

```yaml
services:
  rabbitmq-exporter:
    environment:
      RABBITMQ_EXPORTER_ALERTS_WECHAT_ENABLED: "true"
      RABBITMQ_EXPORTER_ALERTS_WECHAT_WEBHOOK_URL: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"
```

### Kubernetes环境

使用Secret存储敏感信息：

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: wechat-webhook
type: Opaque
stringData:
  webhook-url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"
```

然后在Deployment中引用：

```yaml
env:
- name: RABBITMQ_EXPORTER_ALERTS_WECHAT_WEBHOOK_URL
  valueFrom:
    secretKeyRef:
      name: wechat-webhook
      key: webhook-url
```

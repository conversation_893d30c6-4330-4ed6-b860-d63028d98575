# RabbitMQ Exporter Configuration
rabbitmq:
  # RabbitMQ Management API URL
  url: "http://localhost:15672"
  # Authentication credentials
  username: "guest"
  password: "guest"
  # Connection timeout in seconds
  timeout: 10
  # SSL verification (set to false for self-signed certificates)
  verify_ssl: true

# Exporter settings
exporter:
  # Port to expose metrics on
  port: 9419
  # Metrics collection interval in seconds
  interval: 15
  # Log level (DEBUG, INFO, WARNING, ERROR)
  log_level: "INFO"

# Metrics to collect (set to false to disable specific metrics)
metrics:
  overview: true
  nodes: true
  queues: true
  exchanges: true
  connections: true
  channels: true
  consumers: true

# Alert configuration
alerts:
  # WeChat Work (企业微信) alerts
  wechat:
    enabled: false  # Set to true to enable WeChat alerts
    webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY"
    alert_threshold: 3      # Send alert after N consecutive failures
    alert_interval: 300     # Minimum interval between alerts (seconds)
    send_startup_notification: true   # Send notification when exporter starts
    send_shutdown_notification: true  # Send notification when exporter stops

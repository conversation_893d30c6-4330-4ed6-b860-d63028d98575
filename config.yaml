# RabbitMQ Exporter Configuration
rabbitmq:
  # RabbitMQ Management API URL
  url: "http://localhost:15672"
  # Authentication credentials
  username: "guest"
  password: "guest"
  # Connection timeout in seconds
  timeout: 10
  # SSL verification (set to false for self-signed certificates)
  verify_ssl: true

# Exporter settings
exporter:
  # Port to expose metrics on
  port: 9419
  # Metrics collection interval in seconds
  interval: 15
  # Log level (DEBUG, INFO, WARNING, ERROR)
  log_level: "INFO"

# Metrics to collect (set to false to disable specific metrics)
metrics:
  overview: true
  nodes: true
  queues: true
  exchanges: true
  connections: true
  channels: true
  consumers: true

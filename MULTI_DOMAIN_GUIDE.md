# 多域名监控脚本使用指南

## 概述

多域名监控脚本可以同时监控多个网站/服务的健康状态，支持不同的HTTP方法、请求头和请求体，并在服务异常时通过企业微信发送告警。

## 脚本版本

1. **curl_monitor_with_alert.sh** - 修改后的原始脚本，支持多域名
2. **multi_domain_monitor.sh** - 简化版多域名监控脚本
3. **configurable_monitor.sh** - 可配置版本，支持从配置文件读取监控目标

## 快速开始

### 1. 使用简化版本

```bash
# 直接运行，使用内置配置
bash multi_domain_monitor.sh
```

### 2. 使用可配置版本

```bash
# 创建配置文件
cp monitor_config.conf my_config.conf

# 编辑配置文件
vim my_config.conf

# 运行监控
bash configurable_monitor.sh -c my_config.conf
```

## 配置文件格式

配置文件每行格式：`名称|URL|方法|请求体|请求头`

### 示例配置

```
# 基本GET请求
百度首页|https://www.baidu.com|GET||

# POST请求带JSON数据
API服务|http://api.example.com/health|POST|{"status": "check"}|Content-Type: application/json

# 带认证头的请求
认证API|https://secure-api.com/status|GET||Authorization: Bearer YOUR_TOKEN

# 自定义端口
本地服务|http://localhost:8080/health|GET||
```

### 配置规则

- 使用 `|` 分隔字段
- 空字段保留分隔符（如：`||`）
- `#` 开头的行为注释
- 名称不能包含 `|` 字符
- URL必须包含协议（http://或https://）

## 命令行参数

### configurable_monitor.sh 参数

```bash
Usage: configurable_monitor.sh [OPTIONS]

Options:
  -c, --config FILE     配置文件路径 (默认: monitor_config.conf)
  -i, --interval SEC    检查间隔秒数 (默认: 10)
  -l, --log FILE        日志文件路径 (默认: multi_domain_monitor.log)
  -t, --threshold NUM   告警阈值 (默认: 2)
  -w, --webhook URL     企业微信webhook地址
  -h, --help           显示帮助信息
```

### 使用示例

```bash
# 使用自定义配置文件，30秒检查间隔
./configurable_monitor.sh -c my_sites.conf -i 30

# 设置告警阈值为3次失败
./configurable_monitor.sh -t 3

# 指定日志文件
./configurable_monitor.sh -l /var/log/monitor.log

# 组合参数
./configurable_monitor.sh -c prod.conf -i 60 -t 3 -l /var/log/prod_monitor.log
```

## 企业微信告警配置

### 1. 获取Webhook地址

1. 在企业微信群中添加机器人
2. 复制webhook地址
3. 在脚本中配置 `WECHAT_WEBHOOK_URL`

### 2. 告警类型

- **连接失败告警**：连续失败达到阈值时发送
- **服务恢复通知**：服务从失败状态恢复时发送
- **持续失败提醒**：每5次失败发送一次提醒

### 3. 告警消息格式

**失败告警：**
```
🚨 多域名监控告警 🚨

服务名称: 百度首页
主机名: server-01
监控URL: https://www.baidu.com
连续失败次数: 3
错误信息: HTTP 0 (Exit: 7)
时间: 2024-01-15 10:30:00

建议: 请检查服务状态和网络连接
```

**恢复通知：**
```
✅ 多域名监控恢复 ✅

服务名称: 百度首页
主机名: server-01
监控URL: https://www.baidu.com
状态: 服务已恢复正常
恢复时间: 2024-01-15 10:35:00
之前失败次数: 5
```

## 监控目标配置示例

### 常见服务类型

```bash
# Web服务
网站首页|https://www.example.com|GET||
管理后台|https://admin.example.com|GET||

# API服务
用户API|https://api.example.com/users|GET||Authorization: Bearer TOKEN
订单API|https://api.example.com/orders|POST|{"test": true}|Content-Type: application/json

# 数据库健康检查
MySQL|http://localhost:3306/ping|GET||
Redis|http://localhost:6379/ping|GET||
MongoDB|http://localhost:27017/ping|GET||

# 微服务
用户服务|http://user-service:8080/health|GET||
订单服务|http://order-service:8080/health|GET||
支付服务|http://payment-service:8080/health|GET||

# 第三方依赖
支付宝API|https://openapi.alipay.com|GET||
微信API|https://api.weixin.qq.com|GET||

# 监控工具
Prometheus|http://localhost:9090/-/healthy|GET||
Grafana|http://localhost:3000/api/health|GET||
```

### 复杂配置示例

```bash
# 带复杂JSON的POST请求
复杂API|http://api.example.com/complex|POST|{"user":{"id":1,"name":"test"},"action":"health_check"}|Content-Type: application/json

# 带多个请求头
认证服务|https://auth.example.com/verify|GET||Authorization: Bearer TOKEN; X-API-Key: KEY123

# 自定义端口和路径
内部服务|http://*************:8888/internal/health|GET||X-Internal-Token: SECRET
```

## 运行和管理

### 1. 前台运行

```bash
# 直接运行，可以看到实时日志
bash configurable_monitor.sh -c my_config.conf
```

### 2. 后台运行

```bash
# 后台运行，输出到日志文件
nohup bash configurable_monitor.sh -c my_config.conf > monitor.out 2>&1 &

# 查看进程
ps aux | grep configurable_monitor

# 查看日志
tail -f multi_domain_monitor.log
```

### 3. 系统服务

创建systemd服务文件：

```bash
sudo vim /etc/systemd/system/multi-domain-monitor.service
```

内容：
```ini
[Unit]
Description=Multi Domain Monitor
After=network.target

[Service]
Type=simple
User=monitor
WorkingDirectory=/opt/monitor
ExecStart=/bin/bash /opt/monitor/configurable_monitor.sh -c /opt/monitor/production.conf
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable multi-domain-monitor
sudo systemctl start multi-domain-monitor
sudo systemctl status multi-domain-monitor
```

## 日志管理

### 1. 日志格式

```
[2024-01-15 10:30:00] [百度首页] Checking GET https://www.baidu.com
[2024-01-15 10:30:01] [百度首页] ✅ HTTP 200 (1s)
[2024-01-15 10:30:02] [谷歌首页] ❌ HTTP 0 (Exit: 7) (10s) - Failures: 1
```

### 2. 日志轮转

- 自动轮转：文件大小超过50MB时自动轮转
- 压缩存储：使用gzip压缩旧日志文件
- 文件命名：`log_file.YYYYMMDDHHMMSS.gz`

### 3. 手动日志管理

```bash
# 查看实时日志
tail -f multi_domain_monitor.log

# 查看错误日志
grep "❌" multi_domain_monitor.log

# 查看特定服务日志
grep "\[百度首页\]" multi_domain_monitor.log

# 统计成功率
grep -c "✅" multi_domain_monitor.log
grep -c "❌" multi_domain_monitor.log
```

## 故障排除

### 1. 常见问题

**配置文件格式错误：**
```bash
# 检查配置文件格式
bash configurable_monitor.sh -c config.conf --help
```

**网络连接问题：**
```bash
# 手动测试连接
curl -I https://www.example.com
```

**权限问题：**
```bash
# 检查脚本权限
chmod +x configurable_monitor.sh

# 检查日志文件权限
touch multi_domain_monitor.log
chmod 666 multi_domain_monitor.log
```

### 2. 调试模式

```bash
# 启用bash调试
bash -x configurable_monitor.sh -c config.conf

# 查看详细curl输出
# 在脚本中临时移除 -s 参数
```

### 3. 性能优化

- 调整检查间隔：根据服务重要性设置合适的间隔
- 减少并发：在目标间添加延迟避免网络拥塞
- 日志级别：生产环境可以减少详细日志输出

## 最佳实践

1. **分组配置**：为不同环境创建不同的配置文件
2. **告警策略**：设置合理的告警阈值，避免告警风暴
3. **监控覆盖**：包含关键路径上的所有服务
4. **定期维护**：定期检查和更新监控目标
5. **备份配置**：将配置文件纳入版本控制

这样，您就可以轻松监控多个域名和服务，并在出现问题时及时收到企业微信告警。

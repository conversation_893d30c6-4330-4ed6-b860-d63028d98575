# Ubuntu系统运行指南

## 问题说明

在Ubuntu系统中运行脚本时出现 `[[: not found` 错误，这是因为：

1. 脚本使用了bash特有的 `[[` 语法
2. Ubuntu系统可能使用 `sh` 而不是 `bash` 来执行脚本
3. `sh` 不支持 `[[` 语法和正则表达式匹配

## 解决方案

### 方案1：使用修复后的脚本

我们提供了两个修复版本：

1. **curl_monitor_ubuntu.sh** - 完整功能版本，POSIX兼容
2. **simple_monitor.sh** - 简化版本，更好的兼容性

### 方案2：确保使用bash执行

```bash
# 方法1：直接用bash执行
bash curl_monitor_with_alert.sh

# 方法2：给脚本添加执行权限后用bash执行
chmod +x curl_monitor_with_alert.sh
bash ./curl_monitor_with_alert.sh

# 方法3：确保脚本第一行是 #!/bin/bash
head -1 curl_monitor_with_alert.sh
# 应该显示: #!/bin/bash
```

## 主要修复内容

### 1. 正则表达式匹配

**原代码（bash特有）：**
```bash
if [[ "$curl_output" =~ ^[0-9]+$ ]]; then
```

**修复后（POSIX兼容）：**
```bash
# 方法1：使用grep
if echo "$curl_output" | grep -q '^[0-9]\+$'; then

# 方法2：使用case语句
is_number() {
    case $1 in
        ''|*[!0-9]*) return 1 ;;
        *) return 0 ;;
    esac
}
```

### 2. 条件判断

**原代码：**
```bash
if [ $curl_exit_code -eq 0 ] && [[ "$curl_output" =~ ^[0-9]+$ ]] && [ "$curl_output" -ge 200 ]; then
```

**修复后：**
```bash
if [ $curl_exit_code -eq 0 ] && is_number "$curl_output" && [ "$curl_output" -ge 200 ]; then
```

## 使用步骤

### 1. 下载并配置脚本

```bash
# 下载脚本
wget https://your-server/simple_monitor.sh

# 或者使用修复版本
cp curl_monitor_ubuntu.sh monitor.sh

# 编辑配置
vim monitor.sh
```

### 2. 修改配置参数

```bash
# 修改监控URL
URL="http://your-service.com"

# 修改企业微信webhook
WECHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"

# 修改告警阈值
ALERT_THRESHOLD=3
```

### 3. 运行脚本

```bash
# 给脚本执行权限
chmod +x simple_monitor.sh

# 运行脚本
./simple_monitor.sh

# 或者用bash明确执行
bash simple_monitor.sh

# 后台运行
nohup bash simple_monitor.sh > monitor.log 2>&1 &
```

### 4. 检查运行状态

```bash
# 查看日志
tail -f curl_monitor_slb.log

# 查看进程
ps aux | grep monitor

# 停止脚本
pkill -f simple_monitor.sh
```

## 环境检查

运行前检查系统环境：

```bash
# 检查shell版本
echo $SHELL
bash --version

# 检查必要命令
which curl
which date
which hostname

# 测试企业微信webhook
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"msgtype":"text","text":{"content":"测试消息"}}' \
  "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"
```

## 常见问题

### 1. 权限问题

```bash
# 如果出现权限错误
chmod +x script_name.sh

# 如果日志文件无法写入
touch curl_monitor_slb.log
chmod 666 curl_monitor_slb.log
```

### 2. 命令不存在

```bash
# Ubuntu/Debian安装curl
sudo apt-get update
sudo apt-get install curl

# CentOS/RHEL安装curl
sudo yum install curl
```

### 3. 网络问题

```bash
# 测试网络连接
ping sp.easyview.com.hk
curl -I http://sp.easyview.com.hk

# 测试企业微信API
curl -I https://qyapi.weixin.qq.com
```

## 调试模式

启用调试模式查看详细执行过程：

```bash
# 方法1：使用bash -x
bash -x simple_monitor.sh

# 方法2：在脚本中添加调试
set -x  # 开启调试
set +x  # 关闭调试
```

## 系统服务配置

将监控脚本配置为系统服务：

### 1. 创建systemd服务文件

```bash
sudo vim /etc/systemd/system/curl-monitor.service
```

内容：
```ini
[Unit]
Description=Curl Monitor Service
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/script
ExecStart=/bin/bash /path/to/script/simple_monitor.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. 启用服务

```bash
sudo systemctl daemon-reload
sudo systemctl enable curl-monitor.service
sudo systemctl start curl-monitor.service
sudo systemctl status curl-monitor.service
```

## 性能优化

### 1. 减少日志输出

```bash
# 只记录错误
./simple_monitor.sh 2>&1 | grep -E "(ERROR|Failed|❌)"

# 使用logrotate管理日志
sudo vim /etc/logrotate.d/curl-monitor
```

### 2. 调整检查间隔

```bash
# 根据需要调整INTERVAL值
INTERVAL=5  # 5秒检查一次
INTERVAL=60 # 1分钟检查一次
```

这样修复后的脚本应该能在Ubuntu系统中正常运行，不会再出现 `[[: not found` 错误。

#!/usr/bin/env python3
"""
Example usage of RabbitMQ Exporter components
Demonstrates how to use the individual components programmatically.
"""

import time
import asyncio
from config import Config<PERSON>oader
from rabbitmq_client import Rabbit<PERSON>Q<PERSON>lient
from metrics_collector import <PERSON><PERSON>QCollector
from http_server import MetricsServer


def example_basic_usage():
    """Basic usage example"""
    print("=== Basic Usage Example ===")
    
    # Load configuration
    config_loader = ConfigLoader("config.yaml")
    config = config_loader.load()
    
    # Create RabbitMQ client
    client = RabbitMQClient(
        url=config.rabbitmq.url,
        username=config.rabbitmq.username,
        password=config.rabbitmq.password,
        timeout=config.rabbitmq.timeout,
        verify_ssl=config.rabbitmq.verify_ssl
    )
    
    # Test connection
    if client.health_check():
        print("✓ RabbitMQ connection successful")
    else:
        print("✗ RabbitMQ connection failed")
        return
    
    # Get some basic information
    try:
        overview = client.get_overview()
        print(f"RabbitMQ Version: {overview.get('rabbitmq_version', 'Unknown')}")
        print(f"Cluster Name: {overview.get('cluster_name', 'Unknown')}")
        
        queues = client.get_queues()
        print(f"Total Queues: {len(queues)}")
        
        connections = client.get_connections()
        print(f"Total Connections: {len(connections)}")
        
    except Exception as e:
        print(f"Error fetching data: {e}")


def example_metrics_collection():
    """Metrics collection example"""
    print("\n=== Metrics Collection Example ===")
    
    # Load configuration
    config_loader = ConfigLoader("config.yaml")
    config = config_loader.load()
    
    # Create client and collector
    client = RabbitMQClient(
        url=config.rabbitmq.url,
        username=config.rabbitmq.username,
        password=config.rabbitmq.password
    )
    
    enabled_metrics = {
        'overview': True,
        'nodes': True,
        'queues': True,
        'exchanges': False,  # Disable exchanges for this example
        'connections': True,
        'channels': False,
        'consumers': False
    }
    
    collector = RabbitMQCollector(client, enabled_metrics)
    
    # Collect metrics
    print("Collecting metrics...")
    start_time = time.time()
    
    metrics_count = 0
    for metric in collector.collect():
        metrics_count += 1
        print(f"Metric: {metric.name} (Type: {metric.type})")
        if hasattr(metric, 'samples'):
            for sample in metric.samples[:3]:  # Show first 3 samples
                print(f"  Sample: {sample.name} = {sample.value}")
    
    duration = time.time() - start_time
    print(f"Collected {metrics_count} metrics in {duration:.2f} seconds")


def example_http_server():
    """HTTP server example"""
    print("\n=== HTTP Server Example ===")
    
    # Load configuration
    config_loader = ConfigLoader("config.yaml")
    config = config_loader.load()
    
    # Create components
    client = RabbitMQClient(
        url=config.rabbitmq.url,
        username=config.rabbitmq.username,
        password=config.rabbitmq.password
    )
    
    enabled_metrics = config_loader.get_enabled_metrics()
    collector = RabbitMQCollector(client, enabled_metrics)
    
    # Create and start server
    server = MetricsServer(
        port=9420,  # Use different port for example
        rabbitmq_client=client,
        collector=collector
    )
    
    try:
        server.start()
        print(f"✓ HTTP server started on port 9420")
        print("  Endpoints available:")
        print("    http://localhost:9420/")
        print("    http://localhost:9420/metrics")
        print("    http://localhost:9420/health")
        print("  Press Ctrl+C to stop...")
        
        # Keep server running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nStopping server...")
        server.stop()
        print("✓ Server stopped")


def example_environment_config():
    """Environment configuration example"""
    print("\n=== Environment Configuration Example ===")
    
    import os
    
    # Set some environment variables
    os.environ['RABBITMQ_EXPORTER_RABBITMQ_URL'] = 'http://localhost:15672'
    os.environ['RABBITMQ_EXPORTER_RABBITMQ_USERNAME'] = 'guest'
    os.environ['RABBITMQ_EXPORTER_EXPORTER_PORT'] = '9421'
    os.environ['RABBITMQ_EXPORTER_EXPORTER_LOG_LEVEL'] = 'DEBUG'
    
    # Load configuration (will use environment variables)
    config_loader = ConfigLoader()  # No config file
    config = config_loader.load()
    
    print(f"RabbitMQ URL: {config.rabbitmq.url}")
    print(f"Username: {config.rabbitmq.username}")
    print(f"Exporter Port: {config.exporter.port}")
    print(f"Log Level: {config.exporter.log_level}")
    
    # Clean up environment variables
    for key in list(os.environ.keys()):
        if key.startswith('RABBITMQ_EXPORTER_'):
            del os.environ[key]


def example_error_handling():
    """Error handling example"""
    print("\n=== Error Handling Example ===")
    
    # Try to connect to non-existent RabbitMQ
    client = RabbitMQClient(
        url="http://localhost:99999",  # Invalid port
        username="invalid",
        password="invalid",
        timeout=5
    )
    
    print("Testing connection to invalid RabbitMQ...")
    if client.health_check():
        print("✓ Connection successful (unexpected)")
    else:
        print("✗ Connection failed (expected)")
    
    # Try to fetch data from invalid endpoint
    try:
        overview = client.get_overview()
        print("✓ Data fetched successfully (unexpected)")
    except Exception as e:
        print(f"✗ Data fetch failed (expected): {type(e).__name__}")


if __name__ == '__main__':
    print("RabbitMQ Exporter - Usage Examples")
    print("=" * 50)
    
    try:
        example_basic_usage()
        example_metrics_collection()
        example_environment_config()
        example_error_handling()
        
        # Uncomment to test HTTP server
        # example_http_server()
        
    except Exception as e:
        print(f"Example failed: {e}")
        import traceback
        traceback.print_exc()

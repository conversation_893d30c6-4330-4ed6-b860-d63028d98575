# RabbitMQ Exporter

A Prometheus exporter for RabbitMQ metrics using the Management API.

## Features

- **Comprehensive Metrics**: Collects overview, node, queue, exchange, connection, channel, and consumer metrics
- **Flexible Configuration**: Support for YAML configuration files and environment variables
- **Health Monitoring**: Built-in health check endpoint
- **Structured Logging**: JSON-formatted logs with configurable levels
- **Error Handling**: Robust error handling and recovery mechanisms
- **Docker Ready**: Easy deployment with Docker

## Quick Start

### Prerequisites

- Python 3.7+
- RabbitMQ with Management Plugin enabled
- Access to RabbitMQ Management API

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd rabbitmq-exporter
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Create configuration file:
```bash
cp config.yaml config-local.yaml
# Edit config-local.yaml with your RabbitMQ settings
```

4. Run the exporter:
```bash
python main.py --config config-local.yaml
```

### Using Environment Variables

You can configure the exporter using environment variables:

```bash
export RABBITMQ_EXPORTER_RABBITMQ_URL="http://localhost:15672"
export RABBITMQ_EXPORTER_RABBITMQ_USERNAME="admin"
export RABBITMQ_EXPORTER_RABBITMQ_PASSWORD="password"
export RABBITMQ_EXPORTER_EXPORTER_PORT="9419"
python main.py
```

## Configuration

### Configuration File

The exporter uses YAML configuration files. See `config.yaml` for a complete example.

```yaml
rabbitmq:
  url: "http://localhost:15672"
  username: "guest"
  password: "guest"
  timeout: 10
  verify_ssl: true

exporter:
  port: 9419
  interval: 15
  log_level: "INFO"

metrics:
  overview: true
  nodes: true
  queues: true
  exchanges: true
  connections: true
  channels: true
  consumers: true
```

### Environment Variables

All configuration options can be overridden using environment variables with the prefix `RABBITMQ_EXPORTER_`:

- `RABBITMQ_EXPORTER_RABBITMQ_URL`
- `RABBITMQ_EXPORTER_RABBITMQ_USERNAME`
- `RABBITMQ_EXPORTER_RABBITMQ_PASSWORD`
- `RABBITMQ_EXPORTER_RABBITMQ_TIMEOUT`
- `RABBITMQ_EXPORTER_RABBITMQ_VERIFY_SSL`
- `RABBITMQ_EXPORTER_EXPORTER_PORT`
- `RABBITMQ_EXPORTER_EXPORTER_INTERVAL`
- `RABBITMQ_EXPORTER_EXPORTER_LOG_LEVEL`
- `RABBITMQ_EXPORTER_METRICS_OVERVIEW`
- `RABBITMQ_EXPORTER_METRICS_NODES`
- `RABBITMQ_EXPORTER_METRICS_QUEUES`
- `RABBITMQ_EXPORTER_METRICS_EXCHANGES`
- `RABBITMQ_EXPORTER_METRICS_CONNECTIONS`
- `RABBITMQ_EXPORTER_METRICS_CHANNELS`
- `RABBITMQ_EXPORTER_METRICS_CONSUMERS`

## Endpoints

- `/metrics` - Prometheus metrics endpoint
- `/health` - Health check endpoint
- `/` - Information page

## Metrics

### Overview Metrics

- `rabbitmq_version_info` - RabbitMQ version information
- `rabbitmq_messages_published_total` - Total messages published
- `rabbitmq_messages_delivered_total` - Total messages delivered
- `rabbitmq_messages_acknowledged_total` - Total messages acknowledged
- `rabbitmq_messages_ready` - Messages ready to be delivered
- `rabbitmq_messages_unacknowledged` - Unacknowledged messages
- `rabbitmq_messages_total` - Total messages
- `rabbitmq_connections_total` - Total connections
- `rabbitmq_channels_total` - Total channels
- `rabbitmq_queues_total` - Total queues
- `rabbitmq_consumers_total` - Total consumers

### Node Metrics

- `rabbitmq_node_running` - Node running status
- `rabbitmq_node_memory_used_bytes` - Memory used by node
- `rabbitmq_node_disk_free_bytes` - Free disk space
- `rabbitmq_node_file_descriptors_used` - File descriptors used
- `rabbitmq_node_file_descriptors_total` - Total file descriptors
- `rabbitmq_node_sockets_used` - Sockets used
- `rabbitmq_node_sockets_total` - Total sockets

### Queue Metrics

- `rabbitmq_queue_messages` - Messages in queue
- `rabbitmq_queue_messages_ready` - Ready messages in queue
- `rabbitmq_queue_messages_unacknowledged` - Unacknowledged messages in queue
- `rabbitmq_queue_consumers` - Consumers for queue
- `rabbitmq_queue_memory_bytes` - Memory used by queue
- `rabbitmq_queue_messages_published_total` - Total messages published to queue
- `rabbitmq_queue_messages_delivered_total` - Total messages delivered from queue
- `rabbitmq_queue_messages_acknowledged_total` - Total messages acknowledged from queue

### Exchange Metrics

- `rabbitmq_exchange_messages_published_in_total` - Messages published to exchange
- `rabbitmq_exchange_messages_published_out_total` - Messages published out from exchange

### Connection Metrics

- `rabbitmq_connection_state` - Connection state
- `rabbitmq_connection_channels` - Channels per connection

### Other Metrics

- `rabbitmq_channels_count` - Number of channels
- `rabbitmq_consumers_count` - Number of consumers
- `rabbitmq_exporter_collection_duration_seconds` - Time spent collecting metrics
- `rabbitmq_exporter_collection_errors_total` - Collection errors

## Command Line Options

```bash
python main.py --help
```

Options:
- `--config, -c` - Configuration file path
- `--port, -p` - Port to expose metrics on
- `--rabbitmq-url` - RabbitMQ Management API URL
- `--rabbitmq-username` - RabbitMQ username
- `--rabbitmq-password` - RabbitMQ password
- `--log-level` - Log level (DEBUG, INFO, WARNING, ERROR)
- `--version` - Show version and exit

## Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 9419

CMD ["python", "main.py"]
```

Build and run:

```bash
docker build -t rabbitmq-exporter .
docker run -p 9419:9419 \
  -e RABBITMQ_EXPORTER_RABBITMQ_URL=http://rabbitmq:15672 \
  -e RABBITMQ_EXPORTER_RABBITMQ_USERNAME=admin \
  -e RABBITMQ_EXPORTER_RABBITMQ_PASSWORD=password \
  rabbitmq-exporter
```

## Prometheus Configuration

Add to your `prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['localhost:9419']
    scrape_interval: 15s
```

## Troubleshooting

### Common Issues

1. **Connection refused**: Ensure RabbitMQ Management Plugin is enabled
2. **Authentication failed**: Check username and password
3. **SSL errors**: Set `verify_ssl: false` for self-signed certificates
4. **Permission denied**: Ensure the user has monitoring permissions

### Debugging

Enable debug logging:

```bash
python main.py --log-level DEBUG
```

Check health endpoint:

```bash
curl http://localhost:9419/health
```

## License

MIT License
